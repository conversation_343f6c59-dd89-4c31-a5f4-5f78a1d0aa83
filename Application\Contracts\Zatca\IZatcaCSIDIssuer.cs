﻿using Application.Models.Zatca;
using Domain.Entities;
using Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Contracts.Zatca
{
    public interface IZatcaCSIDIssuer
    {

        Task<CSIDResultModel> OnboardingCSIDAsync(InputCSIDOnboardingModel model);

        Task<CSIDResultModel> RenewCSIDAsync(InputCSIDRenewingModel model);
        Task<InvoiceDataModel> GetInvoiceModel(Domain.Enums.InvoiceType invoiceType, InvoiceTypeCode invoiceTypeCode, string transactionTypeCode);
        Task<InvoiceDataModel> GetInvoiceModel(InvoiceToZatca invoice);
        Task SendInvoiceAsync();
    }
}
