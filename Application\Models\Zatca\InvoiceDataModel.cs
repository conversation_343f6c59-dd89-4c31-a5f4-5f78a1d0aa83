﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Models.Zatca
{
    public class InvoiceDataModel
    {
        public InvoiceDataModel()
        {

        }
        //Inv/2023/2/131231
        /// <summary>
        /// In Case of Invoice will be INV+SerialNumber, Prepayment will be PRP+SerialNumber,
        /// Credit will be CRD+SerialNumber, Debit will be DB+SerialNumber
        /// </summary>
        public string InvoiceNumber { get; set; }
        /// <summary>
        /// GUID / UUID
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// Whether is Standard = 0100000 or Simplified = 0200000
        /// </summary>
        public int InvoiceType { get; set; }
        /// <summary>
        /// Whether is Invoice = 388 or Debit = 383 or Credit = 381 or Prepayment = 386
        /// </summary>
        public int InvoiceTypeCode { get; set; }
        /// <summary>
        /// Whether is Simplified = "0200000" or  Standard = "0100000"
        /// </summary>
        public string TransactionTypeCode { get; set; }
        /// <summary>
        /// When Debit or Credit Type the reason of making Note
        /// </summary>
        public string Notes { get; set; }
        /// <summary>
        /// This is the id of the Details of the invoice
        /// </summary>
        public long Order { get; set; }
        /// <summary>
        /// The date issued in
        /// </summary>
        public string IssueDate { get; set; }
        /// <summary>
        /// The local time that issued in 
        /// </summary>
        public string IssueTime { get; set; }
        /// <summary>
        /// Get the pervious hash of reported/Cleared invoice to be the key of the current invoice
        /// </summary>
        public string PreviousInvoiceHash { get; set; }
        /// <summary>
        /// That's the Invoice Details items
        /// </summary>
        public List<LineItem> Lines { get; set; }
        /// <summary>
        /// The company will be the Supplier of all invoices or notes
        /// </summary>
        public Supplier Supplier { get; set; }
        /// <summary>
        /// That is the total amount of discount applied to the invoice
        /// </summary>
        public double Discount { get; set; }
        /// <summary>
        /// When issunig Debit or credit notes must refer the invoice that has problems 
        /// </summary>
        public string ReferenceId { get; set; }

        public List<PaymentMean> PaymentMeans { get; set; }

        //public int PaymentMeansCode { get; set; } = 10;


        /// <summary>
        /// VAT category taxable amount (BT-116) = ∑(Invoice line net amounts (BT-113)) − Document level allowance amount (BT-93)
        /// VAT category tax amount(BT-117) = VAT category taxable amount(BT-116) × (VAT rate (BT-119) ÷ 100)
        /// </summary>
        public double TaxAmount
        {
            get
            {
                var lineNetAmounts = double.Parse((this.Lines.Sum(l => l.TaxCategory == "S" ? l.TotalWithoutTax : 0) + (this.Charges != null ? this.Charges.Where(ch => ch.TaxCategory == "S").Sum(ch => ch.Amount) : 0)).ToString("0.00"));
                return double.Parse(((lineNetAmounts > 0 ? (lineNetAmounts - Discount) : 0) * Tax / 100).ToString("0.00"));
            }
        }
        /// <summary>
        /// The total amount after adding the Tax to
        /// </summary>
        public double TotalWithTax
        {
            get
            {
                return double.Parse((TaxAmount + TotalWithoutTax).ToString("0.00"));
            }
        }

        /// <summary>
        /// Invoice total amount without VAT (BT109) = Σ Invoice line net amount (BT-131) - Sum of allowances on document level (BT-107)
        /// Item line net amount (BT-131) = ((Item net price (BT-146) ÷ Item price base quantity(BT-149)) 
        ///     × (Invoiced Quantity (BT-129)) −Invoice line allowance amount(BT136)
        /// </summary>
        public double TotalWithoutTax
        {
            get
            {
                return double.Parse(((this.Lines.Sum(l => l.TotalWithoutTax) - Discount) + ChargeTotalAmount).ToString("0.00"));
            }
        }
        /// <summary>
        /// Getting total without Tax or Discount 
        /// </summary>
        public double TotalWithoutTaxAndDiscount
        {
            get
            {
                return double.Parse(this.Lines.Sum(l => l.TotalWithoutTax).ToString("0.00"));
            }
        }
        /// <summary>
        /// if customer paid for spicified items, PrepaidAmount will get the Prepaidmof all items if exists
        /// </summary>
        public double PrepaidAmount
        {
            get
            {
                return double.Parse(this.Lines.Sum(l => l.PrepaidAmount + l.PrepaidTaxAmount).ToString("0.00")); ;
            }
        }
        /// <summary>
        /// Getting what will the customer pay 
        /// </summary>
        public double PaymentAmount
        {
            get
            {
                return TotalWithTax - PrepaidAmount;
            }
        }
        /// <summary>
        /// Getting the count of items in the ivoice 
        /// </summary>
        public int LinesCount
        {
            get { return this.Lines.Count; }
        }
        /// <summary>
        /// The Customer will be null in simplified and Must be assigned in Standard invoice, Credit or Debit
        /// </summary>
        public Customer Customer { get; set; }

        public string DeliveryDate { get; set; }
        /// <summary>
        /// Tax must be integer value not a fraction the default value will be 15 for 15% Tax
        /// </summary>
        public double Tax { get; set; } = 15;
        /// <summary>
        /// Getting the Subtotal by Tax Category ,Tax (The Tax value that will applied), 
        /// TaxAmount (The value tax of the item), TotalwithoutTax (Total amount of the item native without tax),
        /// TaxCategoryReason (for every tax category there's a reason for),
        /// TaxCategoryReasonCode (We can get it from Zatca Documentation)
        /// </summary>
        public List<TaxSubtotal> SubTotals
        {
            get
            {
                var totals = this.Lines.GroupBy(l => l.TaxCategory, (c, r) => new TaxSubtotal
                {
                    TaxCategory = c,
                    Tax = r.FirstOrDefault().Tax,
                    TaxAmount = double.Parse((double.Parse((((r.Sum(l => l.TotalWithoutTax) + (Charges != null ? Charges.Where(ch => ch.TaxCategory == c).Sum(c => c.Amount) : 0)) - double.Parse((c == DiscountTaxCategory ? Discount : 0).ToString("0.00")))).ToString("0.00")) * r.FirstOrDefault().Tax / 100).ToString("0.00")),
                    TotalWithoutTax = double.Parse(((r.Sum(l => l.TotalWithoutTax) + (Charges != null ? Charges.Where(ch => ch.TaxCategory == c).Sum(ch => ch.Amount) : 0)) - double.Parse((c == DiscountTaxCategory ? Discount : 0).ToString("0.00"))).ToString("0.00")),
                    TaxCategoryReason = r.FirstOrDefault().TaxCategoryReason,
                    TaxCategoryReasonCode = r.FirstOrDefault().TaxCategoryReasonCode
                }).ToList();
                return totals;
            }
        }
        /// <summary>
        /// Gets the tax category for the discount. It determines the tax category based on the tax lines.
        /// If there is a standard tax category (denoted by "S"), it returns "S" and sets the Tax to the corresponding tax amount.
        /// If not, it returns the tax category and tax amount from the first available tax line.
        /// </summary>
        public string DiscountTaxCategory
        {
            get
            {
                var linesTaxCategories = this.Lines.Select(l => new { l.TaxCategory, l.Tax });
                var standard = linesTaxCategories.FirstOrDefault(c => c.TaxCategory == "S");
                Tax = standard != null ? standard.Tax : linesTaxCategories.FirstOrDefault().Tax;
                return standard != null ? "S" : linesTaxCategories.FirstOrDefault().TaxCategory;
            }
        }

        /// <summary>
        /// Gets or sets the list of charges applied. Each charge includes details such as description and amount.
        /// </summary>
        public List<Charge> Charges { set; get; }

        /// <summary>
        /// Gets the total amount of all charges. If there are charges, it sums up their amounts and returns the total as a double with two decimal precision.
        /// If there are no charges, it returns 0.
        /// </summary>
        public double ChargeTotalAmount
        {
            get
            {
                if (this.Charges?.Count > 0)
                {
                    return double.Parse(this.Charges?.Sum(c => c.Amount).ToString("0.00"));
                }
                return 0;
            }
        }
    }

    public class TaxSubtotal
    {
        /// <summary>
        /// The total amount without tax. This represents the sum of all amounts before any tax is applied.
        /// </summary>
        public double TotalWithoutTax { set; get; }

        /// <summary>
        /// The total tax amount. This is the calculated tax for the given subtotal.
        /// </summary>
        public double TaxAmount { set; get; }

        /// <summary>
        /// The tax category code. This indicates the specific category of tax applied (e.g., standard, reduced, zero).
        /// </summary>
        public string TaxCategory { get; set; }

        /// <summary>
        /// The specific tax rate applied to the subtotal. This is usually a percentage value.
        /// </summary>
        public double Tax { get; set; }

        /// <summary>
        /// The reason code for the tax category. This provides a reason or justification for the applied tax category.
        /// </summary>
        public string TaxCategoryReasonCode { set; get; }

        /// <summary>
        /// The reason for the tax category. This is a description or explanation of why a particular tax category is applied.
        /// </summary>
        public string TaxCategoryReason { set; get; }
    }


    public class LineItem
    {
        public LineItem()
        {
            //  Id = Guid.NewGuid().ToString();
        }
        /// <summary>
        /// Item id stored in DB
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// Item serial number
        /// </summary>
        public int Index { get; set; }
        /// <summary>
        /// The Actual name of product 
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// Item Quantity
        /// </summary>
        public double Quantity { get; set; }
        /// <summary>
        /// Item Sale price without any tax
        /// </summary>
        public double NetPrice { get; set; }
        /// <summary>
        /// The Discount applied to the product 
        /// </summary>
        public double LineDiscount { get; set; }
        public double PriceDiscount { get; set; }
        /// <summary>
        /// The line VAT amount (KSA-11) must be Invoice line net amount (BT-131) x (Line VAT rate (BT152)/100)
        /// </summary>
        public double TaxAmount
        {
            get
            {
                return double.Parse((TotalWithoutTax * Tax / 100).ToString("0.00"));
            }
        }
        /// <summary>
        /// The invoice line net amount with VAT
        /// </summary>
        public double TotalWithTax
        {
            get
            {
                return double.Parse((TaxAmount + TotalWithoutTax).ToString("0.00"));
            }
        }

        /// <summary>
        /// The invoice line net amount without VAT, and inclusive of line level allowance.
        /// Item line net amount (BT-131) = ((Item net price (BT-146) ÷ Item price base quantity(BT-149)) 
        ///     × (Invoiced Quantity (BT-129)) − Invoice line allowance amount(BT-136)
        /// </summary>
        public double TotalWithoutTax
        {
            get
            {
                //(float)
                return double.Parse(Math.Round(((Quantity * NetPrice + ChargeTotalAmount) - LineDiscount), 3).ToString("0.00"));
            }
        }
        /// <summary>
        /// Item Price is sum of net price and price discount
        /// </summary>
        public double GrossPrice
        {
            get
            {
                return (NetPrice + PriceDiscount);
            }
        }
        /// <summary>
        /// The amount if it's prepayment invoice
        /// </summary>
        public double PrepaidAmount { set; get; }
        /// <summary>
        /// The tax of prepayment invoice amount
        /// </summary>
        public double PrepaidTaxAmount
        {
            get
            {
                return double.Parse((PrepaidAmount * Tax / 100).ToString("0.00"));
            }
        }
        /// <summary>
        ///  Prepayment Invoice Number
        /// </summary>
        public string PrepaymentId { get; set; }
        /// <summary>
        /// Prepayment Invoice Issue Date
        /// </summary>
        public string PrepaymentIssueDate { get; set; }
        /// <summary>
        /// Prepayment Invoice Issue Time 
        /// </summary>
        public string PrepaymentIssueTime { get; set; }
        /// <summary>
        /// Tax integer value not afraction
        /// </summary>
        public double Tax { get; set; }
        /// <summary>
        /// Tax have more than one category
        /// S - Standard Rated Supplies (VAT Category )
        /// Z - Zero Rated Supplies
        /// E - Exempt Supplies
        /// O - Outside Scope of VAT
        /// N - Not Subject to VAT
        /// </summary>
        public string TaxCategory { get; set; } = "S";
        /// <summary>
        /// Standard Rated Supplies:
        /// Reason: Taxable supplies at the standard VAT rate.
        /// Code: “S”
        /// Zero Rated Supplies:
        /// Reason: Taxable supplies at a 0% VAT rate.
        /// Code: “Z”
        /// Exempt Supplies:
        /// Reason: Supplies that are exempt from VAT.
        /// Code: “E”
        /// Outside Scope of VAT:
        /// Reason: Transactions that do not fall within the scope of VAT.
        /// Code: “O”
        /// Not Subject to VAT:
        /// Reason: Supplies or services that are not subject to VAT.
        /// Code: “N”
        /// </summary>
        public string TaxCategoryReasonCode { set; get; } //= "S";
        /// <summary>
        /// Standard Rated Supplies:
        /// Reason: Taxable supplies at the standard VAT rate.
        /// Code: “S”
        /// Zero Rated Supplies:
        /// Reason: Taxable supplies at a 0% VAT rate.
        /// Code: “Z”
        /// Exempt Supplies:
        /// Reason: Supplies that are exempt from VAT.
        /// Code: “E”
        /// Outside Scope of VAT:
        /// Reason: Transactions that do not fall within the scope of VAT.
        /// Code: “O”
        /// Not Subject to VAT:
        /// Reason: Supplies or services that are not subject to VAT.
        /// Code: “N”
        /// </summary>
        public string TaxCategoryReason { set; get; } //= "Taxable supplies at the standard VAT rate.";
        /// <summary>
        /// refers to the practice where a supplier recharges certain costs to their customers
        /// </summary>
        public List<Charge> Charges { set; get; }
        /// <summary>
        /// Getting the total amount of the charges
        /// </summary>
        public double ChargeTotalAmount
        {
            get
            {
                if (this.Charges?.Count > 0)
                {
                    return double.Parse(this.Charges?.Sum(c => c.Amount).ToString("0.00"));
                }
                return 0;
            }
        }
    }

    public class Supplier
    {
        /// <summary>
        /// Seller Tax Registration Number
        /// </summary>
        /// <remarks>
        /// The Seller Tax Registration Number (SellerTRN) is a unique identifier assigned to the seller by the tax authorities for VAT purposes. It is mandatory for compliance with e-invoicing regulations.
        /// </remarks>
        public string SellerTRN { get; set; }

        /// <summary>
        /// The company or Seller name
        /// </summary>
        /// <remarks>
        /// The name of the company or individual selling the goods or services. This should be the official registered name.
        /// </remarks>
        public string SellerName { get; set; }

        /// <summary>
        /// Street name
        /// </summary>
        /// <remarks>
        /// The name of the street where the seller is located. This is part of the seller's address details required for the e-invoice.
        /// </remarks>
        public string StreetName { get; set; }

        /// <summary>
        /// City name
        /// </summary>
        /// <remarks>
        /// The name of the city where the seller is located. This is a mandatory field in the e-invoice to identify the seller's location.
        /// </remarks>
        public string CityName { get; set; }

        /// <summary>
        /// District name
        /// </summary>
        /// <remarks>
        /// The name of the district where the seller is located. This is part of the address details necessary for the e-invoice.
        /// </remarks>
        public string DistrictName { get; set; }

        /// <summary>
        /// Building number
        /// </summary>
        /// <remarks>
        /// The number of the building where the seller is located. This completes the address information required for the e-invoice.
        /// </remarks>
        public string BuildingNumber { get; set; }

        /// <summary>
        /// Identity Type
        /// </summary>
        /// <remarks>
        /// The type of identity document used by the seller. The default is "CRN" which stands for Commercial Registration Number.
        /// </remarks>
        public string IdentityType { get; set; } = "CRN";

        /// <summary>
        /// Identity Number
        /// </summary>
        /// <remarks>
        /// The number of the identity document specified by the IdentityType. This uniquely identifies the seller.
        /// </remarks>
        public string IdentityNumber { get; set; }

        /// <summary>
        /// Country Code
        /// </summary>
        /// <remarks>
        /// The ISO country code of the seller's location. The default is "SA" for Saudi Arabia.
        /// </remarks>
        public string CountryCode { get; set; } = "SA";

        /// <summary>
        /// Additional Street Address
        /// </summary>
        /// <remarks>
        /// Any additional address information for the seller's location that is not covered by the Street Name and Building Number.
        /// </remarks>
        public string AdditionalStreetAddress { get; set; }

        /// <summary>
        /// Postal Code
        /// </summary>
        /// <remarks>
        /// The postal code for the seller's location. This is a required field to complete the address details.
        /// </remarks>
        public string PostalCode { get; set; }

    }

    public class Customer
    {
        /// <summary>
        /// Customer Identity Type
        /// </summary>
        public string IdentityType { get; set; }
        /// <summary>
        /// Customer Identity Number
        /// </summary>
        public string IdentityNumber { get; set; }
        /// <summary>
        /// Customer Street Name
        /// </summary>
        public string StreetName { get; set; }
        /// <summary>
        /// Customer Building Number
        /// </summary>
        public string BuildingNumber { get; set; }
        /// <summary>
        /// Customer City Name
        /// </summary>
        public string CityName { get; set; }
        /// <summary>
        /// Customer Region Name
        /// </summary>
        public string RegionName { get; set; }
        /// <summary>
        /// Customer District Name
        /// </summary>
        public string DistrictName { get; set; }
        /// <summary>
        /// Customer Additional Street Address
        /// </summary>
        public string AdditionalStreetAddress { get; set; }
        /// <summary>
        /// Customer VAT Registration Number
        /// </summary>
        public string VatRegNumber { get; set; }
        /// <summary>
        /// Customer Zip Code
        /// </summary>
        public string ZipCode { get; set; }
        /// <summary>
        /// Customer Name
        /// </summary>
        public string CustomerName { get; set; }
        /// <summary>
        /// Customer Country Code
        /// </summary>
        public string CountryCode { get; set; } = "SA";
    }

    public class Charge
    {
        /// <summary>
        /// Gets or sets the reason code for the charge.
        /// </summary>
        public string ChargeReasonCode { get; set; }

        /// <summary>
        /// Gets or sets the reason description for the charge.
        /// </summary>
        public string ChargeReason { get; set; }

        /// <summary>
        /// Gets or sets the amount of the charge.
        /// </summary>
        public double Amount { get; set; }

        /// <summary>
        /// Gets or sets the tax associated with the charge.
        /// </summary>
        public double Tax { get; set; }

        /// <summary>
        /// Gets or sets the tax category for the charge.
        /// </summary>
        public string TaxCategory { get; set; }
    }


    public class PaymentMean
    {
        public PaymentMean()
        {
        }

        /// <summary>
        /// Gets or sets the payment means code.
        /// </summary>
        public int PaymentMeansCode { get; set; }

        /// <summary>
        /// Gets or sets the notes associated with the payment means.
        /// </summary>
        public string Notes { get; set; }
    }

}
