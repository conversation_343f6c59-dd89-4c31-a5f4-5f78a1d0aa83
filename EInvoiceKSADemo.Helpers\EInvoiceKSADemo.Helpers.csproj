﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <!--<Nullable>enable</Nullable>-->
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Zatca\Enums\**" />
    <Compile Remove="Zatca\Interfaces\**" />
    <Compile Remove="Zatca\Models\**" />
    <EmbeddedResource Remove="Zatca\Enums\**" />
    <EmbeddedResource Remove="Zatca\Interfaces\**" />
    <EmbeddedResource Remove="Zatca\Models\**" />
    <None Remove="Zatca\Enums\**" />
    <None Remove="Zatca\Interfaces\**" />
    <None Remove="Zatca\Models\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Zatca\Files\Data\addQRElement.xsl" />
    <None Remove="Zatca\Files\Data\addSignatureElement.xsl" />
    <None Remove="Zatca\Files\Data\addUBLElement.xsl" />
    <None Remove="Zatca\Files\Data\invoice.xsl" />
    <None Remove="Zatca\Files\Data\qr.xml" />
    <None Remove="Zatca\Files\Data\removeElements.xsl" />
    <None Remove="Zatca\Files\Data\signature.xml" />
    <None Remove="Zatca\Files\Data\ubl.xml" />
    <None Remove="Zatca\Xml\Invoice.xml" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Zatca\Files\Data\addQRElement.xsl" />
    <EmbeddedResource Include="Zatca\Files\Data\addSignatureElement.xsl" />
    <EmbeddedResource Include="Zatca\Files\Data\addUBLElement.xsl" />
    <EmbeddedResource Include="Zatca\Files\Data\invoice.xsl" />
    <EmbeddedResource Include="Zatca\Files\Data\qr.xml" />
    <EmbeddedResource Include="Zatca\Files\Data\removeElements.xsl" />
    <EmbeddedResource Include="Zatca\Files\Data\signature.xml" />
    <EmbeddedResource Include="Zatca\Files\Data\ubl.xml" />
    <EmbeddedResource Include="Zatca\Xml\Invoice.xml" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BouncyCastle.NetCore" Version="1.9.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.4" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="6.0.0" />
    <PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
    <PackageReference Include="System.Security.Cryptography.Xml" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Application\Application.csproj" />
    <ProjectReference Include="..\Domain\Domain.csproj" />
  </ItemGroup>

</Project>
