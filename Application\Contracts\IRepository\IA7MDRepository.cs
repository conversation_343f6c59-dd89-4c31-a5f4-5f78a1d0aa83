﻿using Domain.Entities.A7MD;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Contracts.IRepository
{
    //public interface IA7MDRepository
    //{
    //    Task<IReadOnlyList<InvoicesToZATCA>> GetA7MDNotSentInvoicesAsync();
    //    Task UpdateA7MDSentInvoicesAsync(IReadOnlyList<InvoicesToZATCA> invoices);
    //}
}
