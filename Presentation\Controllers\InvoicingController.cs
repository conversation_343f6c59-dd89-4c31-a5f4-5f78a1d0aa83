﻿using Application.Contracts.IServices;
using Application.Models.Zatca;
using Domain.Entities;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace Presentation.Controllers
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    [ApiVersion("1.0")]
    public class InvoicingController : ControllerBase
    {
        private readonly IZatcaInvoiceSender _zatcaInvoiceSender;
        private readonly IConfiguration _config;

        public InvoicingController(IZatcaInvoiceSender zatcaInvoiceSender,IConfiguration configuration) 
        {
            _zatcaInvoiceSender=zatcaInvoiceSender;
            _config=configuration;
        }
        // POST  api/v1.0/Invoicing
        [HttpPost]
        public async Task<IActionResult> Post([FromBody] InvoiceToZatca Invoice)
        {
            //InvoiceToZatca InvoiceDeserilized= JsonConvert.DeserializeObject<InvoiceToZatca>(Invoice);
            if (Invoice == null)
            {
                var response1 = new
                {
                    StatusCode = 422,
                    Message = "Invoice received not in a valid format.",
                };
                return BadRequest(response1);
            }
            try
            {
                SharedData.APIUrl = _config["ZatcaSettings:ProductionUrl"];
                ZatcaInvoiceResultResponse zatcaInvoiceResult = await _zatcaInvoiceSender.SendInvoiceToZatcaAsync(Invoice);
                return zatcaInvoiceResult.StatusCode==200? Ok(zatcaInvoiceResult) :UnprocessableEntity(zatcaInvoiceResult);
            }
            catch (Exception ex)
            {
                ZatcaInvoiceResultResponse response = new ZatcaInvoiceResultResponse(422, ex.Message, false,null,null);
                return UnprocessableEntity(response);

            }
        }
        // POST  api/v1.0/Simulation/Invoicing
        [HttpPost("Simulation")]
        public async Task<IActionResult> SimulationPost([FromBody] InvoiceToZatca Invoice)
        {
            //InvoiceToZatca InvoiceDeserilized= JsonConvert.DeserializeObject<InvoiceToZatca>(Invoice);
            if (Invoice == null)
            {
                var response1 = new
                {
                    StatusCode = 422,
                    Message = "Invoice received not in a valid format.",
                };
                return UnprocessableEntity(response1);
            }
            try
            {
                SharedData.APIUrl = _config["ZatcaSettings:SimulationUrl"];
                ZatcaInvoiceResultResponse zatcaInvoiceResult = await _zatcaInvoiceSender.SendInvoiceToZatcaAsync(Invoice);
                return zatcaInvoiceResult.StatusCode == 200 ? Ok(zatcaInvoiceResult) : UnprocessableEntity(zatcaInvoiceResult);
            }
            catch (Exception ex)
            {
                ZatcaInvoiceResultResponse response = new ZatcaInvoiceResultResponse(422, ex.Message, false, null, null);
                return UnprocessableEntity(response);

            }
        }

    }
}
