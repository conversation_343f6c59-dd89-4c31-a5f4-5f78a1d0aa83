﻿using Application.Contracts.IServices;
using Application.Models.Zatca;
using Domain.Entities;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Presentation.Models;
using System.ComponentModel.DataAnnotations;

namespace Presentation.Controllers
{
    /// <summary>
    /// Controller for managing ZATCA invoice operations
    /// </summary>
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    [ApiVersion("1.0")]
    [Produces("application/json")]
    [Tags("Invoice Management")]
    public class InvoicingController : ControllerBase
    {
        private readonly IZatcaInvoiceSender _zatcaInvoiceSender;
        private readonly IConfiguration _config;
        private readonly ILogger<InvoicingController> _logger;

        /// <summary>
        /// Initializes a new instance of the InvoicingController
        /// </summary>
        /// <param name="zatcaInvoiceSender">Service for sending invoices to ZATCA</param>
        /// <param name="configuration">Application configuration</param>
        /// <param name="logger">Logger instance</param>
        public InvoicingController(
            IZatcaInvoiceSender zatcaInvoiceSender,
            IConfiguration configuration,
            ILogger<InvoicingController> logger)
        {
            _zatcaInvoiceSender = zatcaInvoiceSender;
            _config = configuration;
            _logger = logger;
        }
        /// <summary>
        /// Submits an invoice to ZATCA production environment for clearance/reporting
        /// </summary>
        /// <param name="invoice">The invoice data to be submitted to ZATCA</param>
        /// <returns>Result of the invoice submission including validation messages and status</returns>
        /// <response code="200">Invoice successfully submitted and processed</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="422">Invoice validation failed</response>
        /// <response code="500">Internal server error</response>
        [HttpPost]
        [ProducesResponseType(typeof(ApiResponse<InvoiceSubmissionResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 422)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> SubmitInvoice([FromBody] InvoiceToZatca invoice)
        {
            if (invoice == null)
            {
                _logger.LogWarning("Invoice submission failed: Null invoice received");
                return BadRequest(ApiResponse.ErrorResponse(
                    "Invoice data is required",
                    400,
                    new List<string> { "Invoice object cannot be null" }));
            }

            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();

                _logger.LogWarning("Invoice submission failed: Validation errors - {Errors}", string.Join(", ", errors));
                return BadRequest(ApiResponse.ValidationErrorResponse(errors));
            }

            try
            {
                _logger.LogInformation("Submitting invoice {InvoiceId} to ZATCA production environment", invoice.InvoiceId);

                SharedData.APIUrl = _config["ZatcaSettings:ProductionUrl"];
                var zatcaResult = await _zatcaInvoiceSender.SendInvoiceToZatcaAsync(invoice);

                var response = MapToInvoiceSubmissionResponse(zatcaResult);

                if (zatcaResult.StatusCode == 200)
                {
                    _logger.LogInformation("Invoice {InvoiceId} successfully submitted to ZATCA", invoice.InvoiceId);
                    return Ok(ApiResponse<InvoiceSubmissionResponse>.SuccessResponse(
                        response,
                        "Invoice successfully submitted to ZATCA"));
                }
                else
                {
                    _logger.LogWarning("Invoice {InvoiceId} submission failed with status {StatusCode}",
                        invoice.InvoiceId, zatcaResult.StatusCode);
                    return UnprocessableEntity(ApiResponse<InvoiceSubmissionResponse>.ErrorResponse(
                        zatcaResult.Message ?? "Invoice submission failed",
                        zatcaResult.StatusCode));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting invoice {InvoiceId} to ZATCA", invoice.InvoiceId);
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "An error occurred while processing the invoice",
                    500,
                    new List<string> { ex.Message }));
            }
        }
        /// <summary>
        /// Submits an invoice to ZATCA simulation environment for testing purposes
        /// </summary>
        /// <param name="invoice">The invoice data to be tested in simulation environment</param>
        /// <returns>Result of the invoice simulation including validation messages and status</returns>
        /// <response code="200">Invoice successfully processed in simulation</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="422">Invoice validation failed</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("Simulation")]
        [ProducesResponseType(typeof(ApiResponse<InvoiceSubmissionResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 422)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> SubmitInvoiceSimulation([FromBody] InvoiceToZatca invoice)
        {
            if (invoice == null)
            {
                _logger.LogWarning("Invoice simulation failed: Null invoice received");
                return BadRequest(ApiResponse.ErrorResponse(
                    "Invoice data is required",
                    400,
                    new List<string> { "Invoice object cannot be null" }));
            }

            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();

                _logger.LogWarning("Invoice simulation failed: Validation errors - {Errors}", string.Join(", ", errors));
                return BadRequest(ApiResponse.ValidationErrorResponse(errors));
            }

            try
            {
                _logger.LogInformation("Submitting invoice {InvoiceId} to ZATCA simulation environment", invoice.InvoiceId);

                SharedData.APIUrl = _config["ZatcaSettings:SimulationUrl"];
                var zatcaResult = await _zatcaInvoiceSender.SendInvoiceToZatcaAsync(invoice);

                var response = MapToInvoiceSubmissionResponse(zatcaResult);

                if (zatcaResult.StatusCode == 200)
                {
                    _logger.LogInformation("Invoice {InvoiceId} successfully processed in simulation", invoice.InvoiceId);
                    return Ok(ApiResponse<InvoiceSubmissionResponse>.SuccessResponse(
                        response,
                        "Invoice successfully processed in simulation environment"));
                }
                else
                {
                    _logger.LogWarning("Invoice {InvoiceId} simulation failed with status {StatusCode}",
                        invoice.InvoiceId, zatcaResult.StatusCode);
                    return UnprocessableEntity(ApiResponse<InvoiceSubmissionResponse>.ErrorResponse(
                        zatcaResult.Message ?? "Invoice simulation failed",
                        zatcaResult.StatusCode));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing invoice {InvoiceId} in simulation", invoice.InvoiceId);
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "An error occurred while processing the invoice in simulation",
                    500,
                    new List<string> { ex.Message }));
            }
        }

        /// <summary>
        /// Maps ZATCA result to standardized response model
        /// </summary>
        private static InvoiceSubmissionResponse MapToInvoiceSubmissionResponse(ZatcaInvoiceResultResponse zatcaResult)
        {
            return new InvoiceSubmissionResponse
            {
                InvoiceHash = zatcaResult.InvoiceHash ?? string.Empty,
                ReportedToZatca = zatcaResult.ReportedToZatca,
                SubmissionDate = DateTime.UtcNow,
                WarningMessages = zatcaResult.WarningMessages?.Select(w => new ValidationMessage
                {
                    Type = w.Type,
                    Code = w.Code,
                    Category = w.Category,
                    Message = w.Message,
                    Status = w.Status
                }).ToList() ?? new List<ValidationMessage>(),
                ErrorMessages = zatcaResult.ErrorMessages?.Select(e => new ValidationMessage
                {
                    Type = e.Type,
                    Code = e.Code,
                    Category = e.Category,
                    Message = e.Message,
                    Status = e.Status
                }).ToList() ?? new List<ValidationMessage>()
            };
        }

    }
}
