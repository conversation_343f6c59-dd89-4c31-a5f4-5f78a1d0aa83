﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities
{
    public class InvoiceToZatca
    {
        /// <summary>
        /// The will be like that xxxxxxxx-xxxx-xxxx-xxxxxxxx
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// The id of the details info of the invoice
        /// </summary>
        public long DetailId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? CompanyJSON { get; set; }
        /// <summary>
        /// This the currency used in the selling must be SAR now
        /// </summary>
        public string? Currency { get; set; }
        /// <summary>
        /// The Invoice Id that Generated after saving
        /// </summary>
        public long? InvoiceId { get; set; }
        /// <summary>
        /// Invoice Creation Date 
        /// </summary>
        public DateTime? InvoiceCreationDate { get; set; }
        /// <summary>
        /// Refere to the sales invoice and the purchase invoice return it as a true(if it's a sales) or false(if it's a purchase)
        /// </summary>
        public bool? IsSalesInvoice { get; set; }
        /// <summary>
        /// When it's a refund invoice the reference id and notes must be filled
        /// </summary>
        public bool? IsRefundInvoice { get; set; }
        public bool? IsDebitInvoice { get; set; }
        public bool? IsTaxInvoice { get; set; }
        /// <summary>
        /// simplified no need to have a customer fields, otherwise the customer fields must be added
        /// </summary>
        public bool? IsSimplifiedInvoice { get; set; }
        /// <summary>
        /// Customer (is serilize to json object) must be added when the invoice is standard
        /// </summary>
        public string? CustomerJson { get; set; }

        public DateTime? InvoiceDeliveryDate { get; set; }
        /// <summary>
        /// When the invoice turned into credit or debit note must assign a reason
        /// </summary>
        public string? RefundReason { get; set; }
        /// <summary>
        /// Total discount applied to the invoice
        /// </summary>
        public decimal? TotalDiscount { get; set; }
        /// <summary>
        /// total tax amount of the items of the invoice
        /// </summary>
        public decimal? TaxAmount { get; set; }
        /// <summary>
        /// Tax percentage is an integer like 15 or 15.000 value not 0.15 or 15%
        /// </summary>
        public decimal? TaxPercentage { get; set; }
        /// <summary>
        /// total amount
        /// </summary>
        public decimal? TotalAmount { get; set; }
        /// <summary>
        /// sum of item prices 
        /// </summary>
        public decimal? TotalForItems { get; set; }
        /// <summary>
        /// get net amount without tax
        /// </summary>
        public decimal? NetWithoutVAT { get; set; }
        /// <summary>
        /// Invoice detail converted o json 
        /// Index =  1, ProductName = "Item", Quantity = 1, NetPrice = 100, Tax = 15, TaxCategory = "S",
        /// </summary>
        public string? InvoiceItemsJson { get; set; }
        /// <summary>
        /// Gets or sets the list of charges applied. Each charge includes details such as description and amount converted o json 
        /// Amount = 20 , ChargeReason = "Cleaning", ChargeReasonCode = "CG" , Tax = 15, TaxCategory  = "S"
        /// Amount = 40 , ChargeReason = "Cleaning", ChargeReasonCode = "CG" , Tax = 00, TaxCategory  = "Z"
        /// </summary>
        public string? ChargesJson { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? InsertFlag { get; set; }
        public int? UpdateFlag { get; set; }
        public int? DeleteFlag { get; set; }
        /// <summary>
        /// when the invoice turned into deleted 
        /// </summary>
        public bool? IsDeleted { get; set; }
        /// <summary>
        /// id of user that created the invoice 
        /// </summary>
        public int? CreatorId { get; set; }
        /// <summary>
        /// the id of the user that modified the invoice 
        /// </summary>
        public int? ModifierId { get; set; }

        public string PaymentMeans { get; set; }
        /// <summary>
        /// Creation Date 
        /// </summary>
        public DateTime? CreationDate { get; set; }
        /// <summary>
        /// Date of Modification
        /// </summary>
        public DateTime? ModificationDate { get; set; }
        /// <summary>
        /// when sending the invoice to zatca if sent and got any result (Cleared or not - Reported or not - Errors ) saved as sent
        /// </summary>
        public bool IsSent { get; set; }
        /// <summary>
        /// if it's a standard invoice when cleared that means it was accepted.
        /// or as a smiplified invoice when reported that means it was accepted.
        /// </summary>
        public bool IsAccepted { get; set; }
        /// <summary>
        /// When sending the invoice by background job it counts the no. of resending
        /// </summary>
        public int CountOfRetries { get; set; }
        /// <summary>
        /// when the invoice turned into credit or debit note must assign the cancelled invoice id as a reference of the note
        /// </summary>
        public string? InvoiceReferenceId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? InvoicePayments { get; set; }
        /// <summary>
        /// when using matual certificate must assign the id of the certificate when sending a new invoice
        /// </summary>
        public string? CertificateDetailsId { get; set; }
    }
}
