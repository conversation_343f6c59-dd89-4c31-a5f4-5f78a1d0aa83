﻿using Application.Contracts.IRepository;
using Application.Contracts.IServices;
using Application.Contracts.Zatca;
using Application.Models.Zatca;
using Domain.Entities;
using Domain.Entities.A7MD;
using Domain.Helpers;
using Microsoft.Extensions.Logging;
using Polly;
using Quartz;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.Jobs
{
    [DisallowConcurrentExecution]
    public class InvoicesReportingBackgroundJob : IJob
    {
        private readonly IInvoiceToZatcaRepository _invoiceToZatcaRepository;
        private readonly IZatcaInvoiceSender _zatcaInvoiceSender;
        private readonly ILogger _logger;
        private readonly IInvoiceRepository _invoiceRepository;
        private readonly ISupplierRepository _supplierRepository;
        private readonly ICertificateConfiguration _certificateConfiguration;
        private readonly IA7MDRepository _a7mdRepository;

        public InvoicesReportingBackgroundJob(
            IInvoiceToZatcaRepository invoiceToZatcaRepository,
            IZatcaInvoiceSender zatcaInvoiceSender,
            ILogger<InvoicesReportingBackgroundJob> logger,
            IInvoiceRepository invoiceRepository,
            ISupplierRepository supplierRepository,
            ICertificateConfiguration certificateConfiguration,
            IA7MDRepository a7mdRepository)
        {
            _invoiceToZatcaRepository = invoiceToZatcaRepository;
            _zatcaInvoiceSender = zatcaInvoiceSender;
            _logger = logger;
            _invoiceRepository = invoiceRepository;
            _supplierRepository = supplierRepository;
            _certificateConfiguration = certificateConfiguration;
            _a7mdRepository = a7mdRepository;
        }

        public  async Task Execute(IJobExecutionContext context)
        {
            _logger.LogInformation("background service has been started to fetch invoice from api");

            #region Calling A7MD Db
           var a7mdInvoices = await _a7mdRepository.GetA7MDNotSentInvoicesAsync();
            _logger.LogInformation($"{a7mdInvoices.Count()} invoices have been fetched from api");

            //if (!a7mdInvoices.Any())
            //    return;
            #endregion

            #region A7MD Invoices To Zatca Invoices Conversion And Saving
            var invoicesToZatca = await MapA7MDInvoicesToZatcaInvoicesAsync(a7mdInvoices);
            
            await _invoiceToZatcaRepository.AddRangeInvoicesAsync(invoicesToZatca.ToList());
            #endregion

            #region Send Invoices To Zatca
            var invoices = await _invoiceToZatcaRepository.GetAllInvoicesToSendAsync();
            
            if (!invoices.Any())
                return;

            var supplier = await _supplierRepository.GetSupplierAsync();
            var certificateDetails = _certificateConfiguration.GetCertificateDetails();

            foreach (var invoice in invoices)
            {
                await _zatcaInvoiceSender.SendInvoiceToZatcaAsync(invoice, supplier, certificateDetails);

                var a7mdInvoice = a7mdInvoices.SingleOrDefault(x=>x.InvoiceId == invoice.InvoiceId);
                if(a7mdInvoice is not null) a7mdInvoice.IsSent = true;
            }
            #endregion

            #region Update A7MD Invoices
            await _a7mdRepository.UpdateA7MDSentInvoicesAsync(a7mdInvoices);
            #endregion
        }

        private async Task<IReadOnlyList<InvoiceToZatca>> MapA7MDInvoicesToZatcaInvoicesAsync(
            IReadOnlyList<InvoicesToZATCA> a7mdInvoices)
        {
            var zatcaInvoices = new List<InvoiceToZatca>();

            foreach (var a7mdInvoice in a7mdInvoices)
            {
                var zatcaInvoice = new InvoiceToZatca
                {
                    CompanyJSON = a7mdInvoice.CompanyJSON,
                    CreationDate = a7mdInvoice.CreationDate,
                    CreatorId = a7mdInvoice.CreatorId,
                    Currency = a7mdInvoice.Currency,
                    CustomerJson = a7mdInvoice.CustomerJson,
                    //CustomerAddressCity = a7mdInvoice.CustomerAddressCity,
                    //CustomerAddressDistrict = a7mdInvoice.CustomerAddressDistrict,
                    //CustomerName = a7mdInvoice.CustomerName,
                    //CustomerId = a7mdInvoice.CustomerId,
                    DeleteFlag = a7mdInvoice.DeleteFlag,
                    DetailId = a7mdInvoice.DetailId,
                    InsertFlag = a7mdInvoice.InsertFlag,
                    InvoiceCreationDate = a7mdInvoice.InvoiceCreationDate,
                    InvoiceId = a7mdInvoice.InvoiceId,
                    InvoiceDeliveryDate = a7mdInvoice.InvoiceDeliveryDate,
                    InvoiceItemsJson = a7mdInvoice.InvoiceItemsJson,
                    IsDeleted = a7mdInvoice.IsDeleted,
                    IsRefundInvoice = a7mdInvoice.IsRefundInvoice,
                    IsSalesInvoice = a7mdInvoice.IsSalesInvoice,
                    IsSimplifiedInvoice = a7mdInvoice.IsSimplifiedInvoice,
                    IsTaxInvoice = a7mdInvoice.IsTaxInvoice,
                    ModificationDate = a7mdInvoice.ModificationDate,
                    ModifierId = a7mdInvoice.ModifierId,
                    NetWithoutVAT = a7mdInvoice.NetWithoutVAT,
                    PaymentMeans = a7mdInvoice.InvoicePayments,
                    RefundReason = a7mdInvoice.RefundReason,
                    TaxAmount = a7mdInvoice.TaxAmount,
                    TaxPercentage = a7mdInvoice.TaxPercentage,
                    TotalAmount = a7mdInvoice.TotalAmount,
                    TotalDiscount = a7mdInvoice.TotalDiscount,
                    TotalForItems = a7mdInvoice.TotalForItems,
                    UpdateFlag = a7mdInvoice.UpdateFlag,
                    IsSent = false
                };

                zatcaInvoices.Add(zatcaInvoice);
            }

            return zatcaInvoices;
        }
    }
}
