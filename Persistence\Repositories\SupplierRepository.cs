﻿using Application.Contracts.IRepository;
using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Persistence.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Persistence.Repositories
{
    public class SupplierRepository: ISupplierRepository
    {
        private readonly ApplicationDbContext _context;

        public SupplierRepository(ApplicationDbContext context)
        {
            _context = context;
        }
        /// <summary>
        /// Add Supplier to DB 
        /// </summary>
        /// <param name="seller">Supplier info</param>
        public async Task AddSupplierAsync(Seller seller)
        {
            await _context.EI_Sellers.AddAsync(seller);
            await _context.SaveChangesAsync();  
        }
        /// <summary>
        /// Getting suppliers list then select the first because it's designed to handle one supplier only
        /// </summary>
        /// <returns>Returns Supplier object</returns>
        public async Task<Seller> GetSupplierAsync()
        {
            var suppliers = await _context.EI_Sellers.ToListAsync();
            return suppliers.FirstOrDefault();
        }
        /// <summary>
        /// Find out if there's any suppliers in DB or not
        /// </summary>
        /// <returns>Returns if any supplier exists on DB or not</returns>
        public async Task<bool> IsSupplierFoundAsync()
        {
            return await _context.EI_Sellers.AnyAsync();
        }
        /// <summary>
        /// Delete the current seller exist
        /// </summary>
        /// <param name="seller"></param>
        /// <returns></returns>
        public async Task DeleteSupplierAsync(Seller seller)
        {
            if (seller!=null)
            {
                 _context.EI_Sellers.Remove(seller); 
                await _context.SaveChangesAsync();
            }        
        }
    }
}
