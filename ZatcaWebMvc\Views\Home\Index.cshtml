﻿@model ZatcaWebMvc.Models.DashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
}

<!-- Page Header -->
<div class="page-header bg-white border-bottom py-4 mb-4">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="page-icon me-3">
                        <i class="bi bi-speedometer2 text-primary fs-2"></i>
                    </div>
                    <div>
                        <h1 class="page-title mb-1">Dashboard</h1>
                        <p class="page-subtitle text-muted mb-0">Welcome to ZATCA E-Invoice Management System</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="page-actions text-md-end">
                    @if (Model?.ApiHealthy == true)
                    {
                        <span class="badge bg-success me-3">
                            <i class="bi bi-check-circle me-1"></i>API Connected
                        </span>
                    }
                    else
                    {
                        <span class="badge bg-danger me-3">
                            <i class="bi bi-x-circle me-1"></i>API Disconnected
                        </span>
                    }
                    <small class="text-muted me-3">Last updated: @DateTime.Now.ToString("HH:mm")</small>
                    <button class="btn btn-primary" onclick="refreshDashboard()">
                        <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        @{
            ViewData["Title"] = "Total Invoices";
            ViewData["Value"] = (Model?.TotalInvoices ?? 0).ToString();
            ViewData["Icon"] = "bi bi-file-earmark-text";
            ViewData["Color"] = "primary";
            ViewData["Subtitle"] = "All invoices in system";
            ViewData["Link"] = Url.Action("Index", "Invoice");
            ViewData["LinkText"] = "View All";
        }
        @await Html.PartialAsync("_StatCard")
    </div>

    <div class="col-xl-3 col-md-6">
        @{
            ViewData["Title"] = "Pending Invoices";
            ViewData["Value"] = (Model?.PendingInvoices ?? 0).ToString();
            ViewData["Icon"] = "bi bi-clock";
            ViewData["Color"] = "warning";
            ViewData["Subtitle"] = "Awaiting submission";
            ViewData["Link"] = Url.Action("Pending", "Invoice");
            ViewData["LinkText"] = "View Pending";
            ViewData["Trend"] = Model?.PendingInvoices > 0 ? "up" : "neutral";
            ViewData["TrendValue"] = Model?.PendingInvoices > 0 ? $"+{Model.PendingInvoices}" : "0";
        }
        @await Html.PartialAsync("_StatCard")
    </div>

    <div class="col-xl-3 col-md-6">
        @{
            ViewData["Title"] = "Submitted Invoices";
            ViewData["Value"] = (Model?.SubmittedInvoices ?? 0).ToString();
            ViewData["Icon"] = "bi bi-check-circle";
            ViewData["Color"] = "success";
            ViewData["Subtitle"] = "Successfully submitted";
            ViewData["Link"] = Url.Action("Submitted", "Invoice");
            ViewData["LinkText"] = "View Submitted";
            ViewData["Trend"] = "up";
            ViewData["TrendValue"] = "+12%";
        }
        @await Html.PartialAsync("_StatCard")
    </div>

    <div class="col-xl-3 col-md-6">
        @{
            ViewData["Title"] = "Total Revenue";
            ViewData["Value"] = (Model?.TotalRevenue ?? 0).ToString("C", new System.Globalization.CultureInfo("ar-SA"));
            ViewData["Icon"] = "bi bi-currency-dollar";
            ViewData["Color"] = "info";
            ViewData["Subtitle"] = "This month";
            ViewData["Link"] = Url.Action("Reports", "Invoice");
            ViewData["LinkText"] = "View Reports";
            ViewData["Trend"] = "up";
            ViewData["TrendValue"] = "+8.5%";
        }
        @await Html.PartialAsync("_StatCard")
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4 mb-4">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent border-0">
                <div class="d-flex align-items-center">
                    <i class="bi bi-lightning me-2 text-primary"></i>
                    <h5 class="card-title mb-0">Quick Actions</h5>
                </div>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <a href="@Url.Action("Create", "Invoice")" class="btn btn-outline-primary w-100 py-3 text-decoration-none">
                            <i class="bi bi-plus-circle fs-4 d-block mb-2"></i>
                            <span class="fw-semibold">Create Invoice</span>
                            <small class="d-block text-muted mt-1">New e-invoice</small>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="@Url.Action("Create", "Certificate")" class="btn btn-outline-success w-100 py-3 text-decoration-none">
                            <i class="bi bi-shield-plus fs-4 d-block mb-2"></i>
                            <span class="fw-semibold">New Certificate</span>
                            <small class="d-block text-muted mt-1">Add certificate</small>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="@Url.Action("Create", "Seller")" class="btn btn-outline-info w-100 py-3 text-decoration-none">
                            <i class="bi bi-building-add fs-4 d-block mb-2"></i>
                            <span class="fw-semibold">Add Seller</span>
                            <small class="d-block text-muted mt-1">Register seller</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent border-0">
                <div class="d-flex align-items-center">
                    <i class="bi bi-info-circle me-2 text-primary"></i>
                    <h5 class="card-title mb-0">System Status</h5>
                </div>
            </div>
            <div class="card-body">
                <div class="status-list">
                    <div class="status-item d-flex align-items-center mb-3 p-3 rounded-3 bg-light">
                        <div class="status-icon me-3">
                            @if (Model?.ApiHealthy == true)
                            {
                                <i class="bi bi-check-circle text-success fs-4"></i>
                            }
                            else
                            {
                                <i class="bi bi-x-circle text-danger fs-4"></i>
                            }
                        </div>
                        <div class="status-content flex-grow-1">
                            <h6 class="mb-1 fw-semibold">ZATCA API</h6>
                            <small class="text-muted">
                                @if (Model?.ApiHealthy == true)
                                {
                                    <span class="badge bg-success bg-opacity-10 text-success">Connected and operational</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger bg-opacity-10 text-danger">Connection failed</span>
                                }
                            </small>
                        </div>
                    </div>

                    <div class="status-item d-flex align-items-center mb-3 p-3 rounded-3 bg-light">
                        <div class="status-icon me-3">
                            <i class="bi bi-shield-check text-success fs-4"></i>
                        </div>
                        <div class="status-content flex-grow-1">
                            <h6 class="mb-1 fw-semibold">Certificates</h6>
                            <small class="text-muted">
                                <span class="badge bg-success bg-opacity-10 text-success">@(Model?.TotalCertificates ?? 0) active</span>
                            </small>
                        </div>
                    </div>

                    <div class="status-item d-flex align-items-center p-3 rounded-3 bg-light">
                        <div class="status-icon me-3">
                            <i class="bi bi-building text-info fs-4"></i>
                        </div>
                        <div class="status-content flex-grow-1">
                            <h6 class="mb-1 fw-semibold">Sellers</h6>
                            <small class="text-muted">
                                <span class="badge bg-info bg-opacity-10 text-info">@(Model?.TotalSellers ?? 0) registered</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Invoices -->
@if (Model?.RecentInvoices?.Any() == true)
{
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0 pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history me-2"></i>Recent Invoices
                        </h5>
                        <a href="@Url.Action("Index", "Invoice")" class="btn btn-sm btn-outline-primary">
                            View All <i class="bi bi-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var invoice in Model.RecentInvoices.Take(5))
                                {
                                    <tr>
                                        <td>
                                            <span class="fw-medium">@invoice.InvoiceNumber</span>
                                        </td>
                                        <td>@invoice.CustomerName</td>
                                        <td>
                                            <span class="fw-medium">@invoice.TotalAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"))</span>
                                        </td>
                                        <td>
                                            @if (invoice.Status == "Submitted")
                                            {
                                                <span class="badge bg-success">@invoice.Status</span>
                                            }
                                            else if (invoice.Status == "Pending")
                                            {
                                                <span class="badge bg-warning">@invoice.Status</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">@invoice.Status</span>
                                            }
                                        </td>
                                        <td>
                                            <small class="text-muted">@invoice.IssueDate.ToString("MMM dd, yyyy")</small>
                                        </td>
                                        <td>
                                            <a href="@Url.Action("Details", "Invoice", new { id = invoice.Id })"
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
else
{
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-file-earmark-text text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 mb-2">No Invoices Yet</h5>
                    <p class="text-muted mb-4">Get started by creating your first invoice</p>
                    <a href="@Url.Action("Create", "Invoice")" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Create First Invoice
                    </a>
                </div>
            </div>
        </div>
    </div>
}
