﻿@model ZatcaWebMvc.Models.DashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
}

<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">
                    <i class="bi bi-speedometer2 me-2"></i>Dashboard
                </h1>
                <p class="text-muted mb-0">Welcome to ZATCA E-Invoice Management System</p>
            </div>
            <div class="d-flex align-items-center">
                @if (Model?.ApiHealthy == true)
                {
                    <span class="badge bg-success me-2">
                        <i class="bi bi-check-circle me-1"></i>API Connected
                    </span>
                }
                else
                {
                    <span class="badge bg-danger me-2">
                        <i class="bi bi-x-circle me-1"></i>API Disconnected
                    </span>
                }
                <small class="text-muted">Last updated: @DateTime.Now.ToString("HH:mm")</small>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-file-earmark-text text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Total Invoices</h6>
                        <h3 class="mb-0">@(Model?.TotalInvoices ?? 0)</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-clock text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Pending Invoices</h6>
                        <h3 class="mb-0">@(Model?.PendingInvoices ?? 0)</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-check-circle text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Submitted Invoices</h6>
                        <h3 class="mb-0">@(Model?.SubmittedInvoices ?? 0)</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-currency-dollar text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Total Revenue</h6>
                        <h3 class="mb-0">@((Model?.TotalRevenue ?? 0).ToString("C", new System.Globalization.CultureInfo("ar-SA")))</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4 mb-4">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent border-0 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightning me-2"></i>Quick Actions
                    </h5>
                </div>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <a href="@Url.Action("Create", "Invoice")" class="btn btn-outline-primary w-100 py-3">
                            <i class="bi bi-plus-circle fs-4 d-block mb-2"></i>
                            <span>Create Invoice</span>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="@Url.Action("Create", "Certificate")" class="btn btn-outline-success w-100 py-3">
                            <i class="bi bi-shield-plus fs-4 d-block mb-2"></i>
                            <span>New Certificate</span>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="@Url.Action("Create", "Seller")" class="btn btn-outline-info w-100 py-3">
                            <i class="bi bi-building-add fs-4 d-block mb-2"></i>
                            <span>Add Seller</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>System Status
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        @if (Model?.ApiHealthy == true)
                        {
                            <i class="bi bi-check-circle text-success fs-5"></i>
                        }
                        else
                        {
                            <i class="bi bi-x-circle text-danger fs-5"></i>
                        }
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1">ZATCA API</h6>
                        <small class="text-muted">
                            @if (Model?.ApiHealthy == true)
                            {
                                <span>Connected and operational</span>
                            }
                            else
                            {
                                <span>Connection failed</span>
                            }
                        </small>
                    </div>
                </div>

                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <i class="bi bi-shield-check text-success fs-5"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1">Certificates</h6>
                        <small class="text-muted">@(Model?.TotalCertificates ?? 0) active</small>
                    </div>
                </div>

                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="bi bi-building text-info fs-5"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1">Sellers</h6>
                        <small class="text-muted">@(Model?.TotalSellers ?? 0) registered</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Invoices -->
@if (Model?.RecentInvoices?.Any() == true)
{
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0 pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history me-2"></i>Recent Invoices
                        </h5>
                        <a href="@Url.Action("Index", "Invoice")" class="btn btn-sm btn-outline-primary">
                            View All <i class="bi bi-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var invoice in Model.RecentInvoices.Take(5))
                                {
                                    <tr>
                                        <td>
                                            <span class="fw-medium">@invoice.InvoiceNumber</span>
                                        </td>
                                        <td>@invoice.CustomerName</td>
                                        <td>
                                            <span class="fw-medium">@invoice.TotalAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"))</span>
                                        </td>
                                        <td>
                                            @if (invoice.Status == "Submitted")
                                            {
                                                <span class="badge bg-success">@invoice.Status</span>
                                            }
                                            else if (invoice.Status == "Pending")
                                            {
                                                <span class="badge bg-warning">@invoice.Status</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">@invoice.Status</span>
                                            }
                                        </td>
                                        <td>
                                            <small class="text-muted">@invoice.IssueDate.ToString("MMM dd, yyyy")</small>
                                        </td>
                                        <td>
                                            <a href="@Url.Action("Details", "Invoice", new { id = invoice.Id })"
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
else
{
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-file-earmark-text text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 mb-2">No Invoices Yet</h5>
                    <p class="text-muted mb-4">Get started by creating your first invoice</p>
                    <a href="@Url.Action("Create", "Invoice")" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Create First Invoice
                    </a>
                </div>
            </div>
        </div>
    </div>
}
