using Microsoft.AspNetCore.Mvc;
using ZatcaWebMvc.Services;

namespace ZatcaWebMvc.Controllers
{
    /// <summary>
    /// Controller for testing API service functionality
    /// </summary>
    public class ApiTestController : Controller
    {
        private readonly IZatcaApiService _zatcaApiService;
        private readonly ILogger<ApiTestController> _logger;

        public ApiTestController(IZatcaApiService zatcaApiService, ILogger<ApiTestController> logger)
        {
            _zatcaApiService = zatcaApiService;
            _logger = logger;
        }

        /// <summary>
        /// Test API connectivity and health
        /// </summary>
        public async Task<IActionResult> Index()
        {
            try
            {
                // Test basic health check
                var isHealthy = await _zatcaApiService.IsApiHealthyAsync();
                ViewBag.IsHealthy = isHealthy;

                // Test detailed health status
                var healthStatus = await _zatcaApiService.GetApiHealthStatusAsync();
                ViewBag.HealthStatus = healthStatus;

                // Test authentication
                var authTest = await _zatcaApiService.TestAuthenticationAsync();
                ViewBag.AuthTest = authTest;

                // Test getting invoices
                var invoicesResponse = await _zatcaApiService.GetAllInvoicesAsync();
                ViewBag.InvoicesResponse = invoicesResponse;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing API service");
                ViewBag.Error = ex.Message;
                return View();
            }
        }

        /// <summary>
        /// Test API service methods via AJAX
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> TestMethod(string method)
        {
            try
            {
                object result = method switch
                {
                    "health" => await _zatcaApiService.IsApiHealthyAsync(),
                    "healthStatus" => await _zatcaApiService.GetApiHealthStatusAsync(),
                    "auth" => await _zatcaApiService.TestAuthenticationAsync(),
                    "invoices" => await _zatcaApiService.GetAllInvoicesAsync(),
                    "certificates" => await _zatcaApiService.GetAllCertificatesAsync(),
                    "sellers" => await _zatcaApiService.GetAllSellersAsync(),
                    _ => new { error = "Unknown method" }
                };

                return Json(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing API method {Method}", method);
                return Json(new { success = false, error = ex.Message });
            }
        }
    }
}
