﻿using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Persistence.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options) { }

        public DbSet<CertificateSettings> EI_CertificateSettings { get; set; }
        public DbSet<Invoice> EI_Invoices { get; set; }
        public DbSet<Seller> EI_Sellers { get; set; }
        public DbSet<SellerIdentity> EI_SellerIdentities { get; set; }
        public DbSet<InvoiceType> EI_InvoiceTypes { get; set; }
        public DbSet<InvoiceToZatca> EI_InvoiceToZatcas { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
        }
    }
}
