﻿using Application.Models.Zatca;
using Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Contracts.Zatca
{
    public interface ICertificateConfiguration
    {
        CertificateSettings GetCertificateDetails();
        Task<IEnumerable<CertificateSettings>> GetCertificatesDetails();
        Task<CertificateSettings> GetCertificateDetails(string companyId);
        Task SaveCsrAndPK(InputCsrModel model);
        Task UpdateCertificate(CSIDResultModel model);
        Task RemoveCertificate(CertificateSettings certificateSettings);
    }
}
