using Application.Models.Zatca;

namespace Presentation.Models
{
    /// <summary>
    /// Response model for invoice submission to ZATCA
    /// </summary>
    public class InvoiceSubmissionResponse
    {
        /// <summary>
        /// Unique invoice hash generated by ZATCA
        /// </summary>
        public string InvoiceHash { get; set; } = string.Empty;

        /// <summary>
        /// QR code for the invoice
        /// </summary>
        public string QrCode { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if the invoice was reported to ZATCA
        /// </summary>
        public bool ReportedToZatca { get; set; }

        /// <summary>
        /// Reporting status from ZATCA
        /// </summary>
        public string ReportingStatus { get; set; } = string.Empty;

        /// <summary>
        /// Clearance status from ZATCA
        /// </summary>
        public string ClearanceStatus { get; set; } = string.Empty;

        /// <summary>
        /// Date and time when the invoice was submitted
        /// </summary>
        public DateTime SubmissionDate { get; set; }

        /// <summary>
        /// Warning messages from ZATCA validation
        /// </summary>
        public List<ValidationMessage> WarningMessages { get; set; } = new List<ValidationMessage>();

        /// <summary>
        /// Error messages from ZATCA validation
        /// </summary>
        public List<ValidationMessage> ErrorMessages { get; set; } = new List<ValidationMessage>();

        /// <summary>
        /// Base64 encoded invoice XML
        /// </summary>
        public string InvoiceBase64 { get; set; } = string.Empty;

        /// <summary>
        /// Signed XML content
        /// </summary>
        public string SignedXml { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for certificate creation
    /// </summary>
    public class CertificateCreationResponse
    {
        /// <summary>
        /// Generated Certificate Signing Request (CSR)
        /// </summary>
        public string Csr { get; set; } = string.Empty;

        /// <summary>
        /// Private key associated with the certificate
        /// </summary>
        public string PrivateKey { get; set; } = string.Empty;

        /// <summary>
        /// Binary security token from ZATCA
        /// </summary>
        public string BinarySecurityToken { get; set; } = string.Empty;

        /// <summary>
        /// Secret key from ZATCA
        /// </summary>
        public string Secret { get; set; } = string.Empty;

        /// <summary>
        /// Request ID from ZATCA
        /// </summary>
        public long RequestId { get; set; }

        /// <summary>
        /// Certificate expiration date
        /// </summary>
        public DateTime? ExpirationDate { get; set; }

        /// <summary>
        /// Indicates if this is a production certificate
        /// </summary>
        public bool IsProduction { get; set; }
    }

    /// <summary>
    /// Validation message from ZATCA
    /// </summary>
    public class ValidationMessage
    {
        /// <summary>
        /// Type of validation message (ERROR, WARNING, INFO)
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Validation code
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Category of the validation
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// Validation message text
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Status of the validation
        /// </summary>
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for seller/supplier information
    /// </summary>
    public class SellerResponse
    {
        /// <summary>
        /// Seller/Supplier ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Organization name
        /// </summary>
        public string OrganizationName { get; set; } = string.Empty;

        /// <summary>
        /// VAT registration number
        /// </summary>
        public string VatNumber { get; set; } = string.Empty;

        /// <summary>
        /// Business address
        /// </summary>
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// City
        /// </summary>
        public string City { get; set; } = string.Empty;

        /// <summary>
        /// District/Region
        /// </summary>
        public string District { get; set; } = string.Empty;

        /// <summary>
        /// Country code (e.g., SA for Saudi Arabia)
        /// </summary>
        public string CountryCode { get; set; } = string.Empty;

        /// <summary>
        /// Contact email
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Contact phone number
        /// </summary>
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// Business category
        /// </summary>
        public string BusinessCategory { get; set; } = string.Empty;

        /// <summary>
        /// Date when the seller was created
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Date when the seller was last modified
        /// </summary>
        public DateTime? ModifiedDate { get; set; }
    }

    /// <summary>
    /// Request model for creating/updating seller information
    /// </summary>
    public class SellerRequest
    {
        /// <summary>
        /// Organization name
        /// </summary>
        public string OrganizationName { get; set; } = string.Empty;

        /// <summary>
        /// VAT registration number (15 digits starting and ending with 3)
        /// </summary>
        public string VatNumber { get; set; } = string.Empty;

        /// <summary>
        /// Business address
        /// </summary>
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// City
        /// </summary>
        public string City { get; set; } = string.Empty;

        /// <summary>
        /// District/Region
        /// </summary>
        public string District { get; set; } = string.Empty;

        /// <summary>
        /// Country code (default: SA)
        /// </summary>
        public string CountryCode { get; set; } = "SA";

        /// <summary>
        /// Contact email
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Contact phone number
        /// </summary>
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// Business category
        /// </summary>
        public string BusinessCategory { get; set; } = string.Empty;
    }
}
