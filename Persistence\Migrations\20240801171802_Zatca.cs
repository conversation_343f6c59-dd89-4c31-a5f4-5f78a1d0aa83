﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Persistence.Migrations
{
    public partial class Zatca : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_Sellers",
                table: "Sellers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SellerIdentities",
                table: "SellerIdentities");

            migrationBuilder.DropPrimaryKey(
                name: "PK_InvoiceTypes",
                table: "InvoiceTypes");

            migrationBuilder.DropPrimaryKey(
                name: "PK_InvoiceToZatcas",
                table: "InvoiceToZatcas");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Invoices",
                table: "Invoices");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CertificateSettings",
                table: "CertificateSettings");

            migrationBuilder.DropColumn(
                name: "CompanyAddress",
                table: "InvoiceToZatcas");

            migrationBuilder.DropColumn(
                name: "CompanyAddressCity",
                table: "InvoiceToZatcas");

            migrationBuilder.DropColumn(
                name: "CompanyAddressDistrict",
                table: "InvoiceToZatcas");

            migrationBuilder.RenameTable(
                name: "Sellers",
                newName: "EI_Sellers");

            migrationBuilder.RenameTable(
                name: "SellerIdentities",
                newName: "EI_SellerIdentities");

            migrationBuilder.RenameTable(
                name: "InvoiceTypes",
                newName: "EI_InvoiceTypes");

            migrationBuilder.RenameTable(
                name: "InvoiceToZatcas",
                newName: "EI_InvoiceToZatcas");

            migrationBuilder.RenameTable(
                name: "Invoices",
                newName: "EI_Invoices");

            migrationBuilder.RenameTable(
                name: "CertificateSettings",
                newName: "EI_CertificateSettings");

            migrationBuilder.RenameColumn(
                name: "CompanyTaxNumber",
                table: "EI_InvoiceToZatcas",
                newName: "CompanyJSON");

            migrationBuilder.RenameColumn(
                name: "CompanyName",
                table: "EI_InvoiceToZatcas",
                newName: "ChargesJson");

            migrationBuilder.AddColumn<string>(
                name: "DescriptionEn",
                table: "EI_InvoiceTypes",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDebitInvoice",
                table: "EI_InvoiceToZatcas",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CertificateId",
                table: "EI_Invoices",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "EI_CertificateSettings",
                type: "int",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_EI_Sellers",
                table: "EI_Sellers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_EI_SellerIdentities",
                table: "EI_SellerIdentities",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_EI_InvoiceTypes",
                table: "EI_InvoiceTypes",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_EI_InvoiceToZatcas",
                table: "EI_InvoiceToZatcas",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_EI_Invoices",
                table: "EI_Invoices",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_EI_CertificateSettings",
                table: "EI_CertificateSettings",
                column: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_EI_Sellers",
                table: "EI_Sellers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_EI_SellerIdentities",
                table: "EI_SellerIdentities");

            migrationBuilder.DropPrimaryKey(
                name: "PK_EI_InvoiceTypes",
                table: "EI_InvoiceTypes");

            migrationBuilder.DropPrimaryKey(
                name: "PK_EI_InvoiceToZatcas",
                table: "EI_InvoiceToZatcas");

            migrationBuilder.DropPrimaryKey(
                name: "PK_EI_Invoices",
                table: "EI_Invoices");

            migrationBuilder.DropPrimaryKey(
                name: "PK_EI_CertificateSettings",
                table: "EI_CertificateSettings");

            migrationBuilder.DropColumn(
                name: "DescriptionEn",
                table: "EI_InvoiceTypes");

            migrationBuilder.DropColumn(
                name: "IsDebitInvoice",
                table: "EI_InvoiceToZatcas");

            migrationBuilder.DropColumn(
                name: "CertificateId",
                table: "EI_Invoices");

            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "EI_CertificateSettings");

            migrationBuilder.RenameTable(
                name: "EI_Sellers",
                newName: "Sellers");

            migrationBuilder.RenameTable(
                name: "EI_SellerIdentities",
                newName: "SellerIdentities");

            migrationBuilder.RenameTable(
                name: "EI_InvoiceTypes",
                newName: "InvoiceTypes");

            migrationBuilder.RenameTable(
                name: "EI_InvoiceToZatcas",
                newName: "InvoiceToZatcas");

            migrationBuilder.RenameTable(
                name: "EI_Invoices",
                newName: "Invoices");

            migrationBuilder.RenameTable(
                name: "EI_CertificateSettings",
                newName: "CertificateSettings");

            migrationBuilder.RenameColumn(
                name: "CompanyJSON",
                table: "InvoiceToZatcas",
                newName: "CompanyTaxNumber");

            migrationBuilder.RenameColumn(
                name: "ChargesJson",
                table: "InvoiceToZatcas",
                newName: "CompanyName");

            migrationBuilder.AddColumn<string>(
                name: "CompanyAddress",
                table: "InvoiceToZatcas",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyAddressCity",
                table: "InvoiceToZatcas",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyAddressDistrict",
                table: "InvoiceToZatcas",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_Sellers",
                table: "Sellers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SellerIdentities",
                table: "SellerIdentities",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_InvoiceTypes",
                table: "InvoiceTypes",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_InvoiceToZatcas",
                table: "InvoiceToZatcas",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Invoices",
                table: "Invoices",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CertificateSettings",
                table: "CertificateSettings",
                column: "Id");
        }
    }
}
