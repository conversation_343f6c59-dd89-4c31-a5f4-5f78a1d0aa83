@model ZatcaWebMvc.Models.CertificateCreateRequest
@{
    ViewData["Title"] = "Create Certificate";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-action="Index">Certificates</a></li>
                    <li class="breadcrumb-item active">Create New</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="bi bi-shield-plus me-2"></i>Create New Certificate
            </h1>
            <p class="text-muted mb-0">Generate a new ZATCA compliance certificate</p>
        </div>
        <div>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Back to List
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-form-check me-1"></i>Certificate Information
                    </h6>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                        @if (ViewBag.ErrorMessage != null)
                        {
                            <div class="alert alert-danger" role="alert">
                                <i class="bi bi-exclamation-triangle me-2"></i>@ViewBag.ErrorMessage
                            </div>
                        }

                        <!-- Organization Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-building me-1"></i>Organization Information
                                </h5>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="OrganizationName" class="form-label"></label>
                                    <input asp-for="OrganizationName" class="form-control" placeholder="Enter organization name" />
                                    <span asp-validation-for="OrganizationName" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="BranchName" class="form-label"></label>
                                    <input asp-for="BranchName" class="form-control" placeholder="Enter branch name" />
                                    <span asp-validation-for="BranchName" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="BusinessCategory" class="form-label"></label>
                                    <select asp-for="BusinessCategory" class="form-select">
                                        <option value="">Select business category</option>
                                        <option value="Retail">Retail</option>
                                        <option value="Wholesale">Wholesale</option>
                                        <option value="Manufacturing">Manufacturing</option>
                                        <option value="Services">Services</option>
                                        <option value="Construction">Construction</option>
                                        <option value="Healthcare">Healthcare</option>
                                        <option value="Education">Education</option>
                                        <option value="Technology">Technology</option>
                                        <option value="Other">Other</option>
                                    </select>
                                    <span asp-validation-for="BusinessCategory" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="VATNumber" class="form-label"></label>
                                    <input asp-for="VATNumber" class="form-control" placeholder="3XXXXXXXXXXXXXXXXX3" 
                                           pattern="^3\d{13}3$" title="VAT Number must be 15 digits starting and ending with 3" />
                                    <div class="form-text">Must be 15 digits starting and ending with 3</div>
                                    <span asp-validation-for="VATNumber" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="Email" class="form-label"></label>
                                    <input asp-for="Email" class="form-control" placeholder="<EMAIL>" />
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="CountryName" class="form-label"></label>
                                    <select asp-for="CountryName" class="form-select">
                                        <option value="SA" selected>Saudi Arabia</option>
                                    </select>
                                    <span asp-validation-for="CountryName" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Technical Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-gear me-1"></i>Technical Information
                                </h5>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="InvoiceType" class="form-label"></label>
                                    <select asp-for="InvoiceType" class="form-select">
                                        <option value="">Select invoice type</option>
                                        <option value="Standard">Standard Invoice</option>
                                        <option value="Simplified">Simplified Invoice</option>
                                        <option value="Both">Both Types</option>
                                    </select>
                                    <span asp-validation-for="InvoiceType" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="LocationAddress" class="form-label"></label>
                                    <textarea asp-for="LocationAddress" class="form-control" rows="3" 
                                              placeholder="Enter complete business address"></textarea>
                                    <span asp-validation-for="LocationAddress" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Otp" class="form-label"></label>
                                    <input asp-for="Otp" class="form-control" placeholder="Enter OTP from ZATCA" />
                                    <div class="form-text">One-time password received from ZATCA</div>
                                    <span asp-validation-for="Otp" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input asp-for="IsProduction" class="form-check-input" />
                                        <label asp-for="IsProduction" class="form-check-label">
                                            Production Environment
                                        </label>
                                        <div class="form-text">Check this for production certificates (live environment)</div>
                                    </div>
                                    <span asp-validation-for="IsProduction" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Important Notice -->
                        <div class="alert alert-info" role="alert">
                            <h6 class="alert-heading">
                                <i class="bi bi-info-circle me-1"></i>Important Information
                            </h6>
                            <ul class="mb-0">
                                <li>Ensure all information is accurate as it will be used for ZATCA compliance</li>
                                <li>The VAT number must be registered with ZATCA</li>
                                <li>OTP is required and must be obtained from ZATCA portal</li>
                                <li>Production certificates are for live transactions only</li>
                            </ul>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-shield-plus me-1"></i>Create Certificate
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // VAT Number formatting
        document.getElementById('VATNumber').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
            
            if (value.length > 0 && !value.startsWith('3')) {
                value = '3' + value.substring(1);
            }
            
            if (value.length > 15) {
                value = value.substring(0, 15);
            }
            
            if (value.length === 15 && !value.endsWith('3')) {
                value = value.substring(0, 14) + '3';
            }
            
            e.target.value = value;
        });

        // Form validation enhancement
        document.querySelector('form').addEventListener('submit', function(e) {
            const vatNumber = document.getElementById('VATNumber').value;
            const vatPattern = /^3\d{13}3$/;
            
            if (!vatPattern.test(vatNumber)) {
                e.preventDefault();
                alert('VAT Number must be exactly 15 digits starting and ending with 3');
                document.getElementById('VATNumber').focus();
                return false;
            }
        });
    </script>
}
