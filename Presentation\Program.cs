using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Persistence.Data;
using Persistence.Seed;

namespace Presentation
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            

            var app = builder.ConfigureServices();

            // Configure the HTTP request pipeline.
            if (!app.Environment.IsDevelopment())
            {
                app.UseExceptionHandler("/Home/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            // Enable Swagger in all environments (you can restrict to Development if needed)
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "ZATCA E-Invoice API v1.0");
                c.RoutePrefix = "swagger"; // Set Swagger UI at /swagger
                c.DocumentTitle = "ZATCA E-Invoice API Documentation";
                c.DefaultModelsExpandDepth(-1); // Hide models section by default
                c.DisplayRequestDuration();
                c.EnableFilter();
                c.EnableDeepLinking();
            });

            //app.UseMiddleware<ExceptionHandlerMiddleware>();
            
            app.UseStatusCodePagesWithReExecute("/errors/{0}");
            
            app.UseHttpsRedirection();
            app.UseStaticFiles();

            app.UseRouting();

            app.UseAuthorization();

            app.MapControllerRoute(
                name: "default",
                pattern: "{controller=Home}/{action=Index}/{id?}");

            // Database seeding commented out for testing without database connection
            // var scope = app.Services.CreateScope();
            // var services = scope.ServiceProvider;

            // try
            // {
            //     var context = services.GetRequiredService<ApplicationDbContext>();
            //     await ApplicationDbContextSeed.SeedAsync(context);
            // }
            // catch (Exception ex)
            // {
            //     var logger = services.GetRequiredService<ILogger<Program>>();
            //     logger.LogError(ex, ex.Message);
            // }

            app.Run();
        }
    }
}