﻿using Application.Contracts.IRepository;
using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Persistence.Data;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Formats.Asn1.AsnWriter;

namespace Persistence.Repositories
{
    public class InvoiceRepository : IInvoiceRepository
    {
        private readonly ApplicationDbContext _context;
        private readonly IConfiguration _configuration;

        public InvoiceRepository(ApplicationDbContext context, IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
        }
        /// <summary>
        /// Adding invoice that reported/Cleared to DB to get some info later (such as hash code) or use it to make note if there is a problem
        /// </summary>
        /// <param name="invoice">Cleared/ Reported Invoice info</param>
        /// <returns>Also returns the invoice info to be used in other method</returns>
        public async Task<Invoice> AddInvoiceAsync(Invoice invoice)
        {
            await _context.EI_Invoices.AddAsync(invoice);  
            await _context.SaveChangesAsync();

            return invoice;
        }
        /// <summary>
        /// Getting pervoius invoice hash to assign with the new invoice need to be Cleared/Reported as a key 
        /// </summary>
        /// <returns>The last Invoice hash</returns>
        public async Task<string> GetPreviousHashAsync()
        {
            var previousInvoice = await _context.EI_Invoices.OrderByDescending(x => x.SubmissionDate)
                .FirstOrDefaultAsync();

            return previousInvoice is not null ? previousInvoice.InvoiceHash : "";
        }
        public async Task<string> GetPreviousHashAsync(string CertificateId)
        {
            var previousInvoice = await _context.EI_Invoices.Where(x=> x.CertificateId== CertificateId).OrderByDescending(x => x.SubmissionDate)
                .FirstOrDefaultAsync();

            return previousInvoice is not null ? previousInvoice.InvoiceHash : "";
        }
    }
}
