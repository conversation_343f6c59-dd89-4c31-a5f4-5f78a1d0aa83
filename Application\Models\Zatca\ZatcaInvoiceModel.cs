﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Models.Zatca
{
    public class ZatcaInvoiceReportResult : ZatcaInvoiceResult
    {

    }
    public class ZatcaInvoiceResult
    {
        public ZatcaInvoiceModel Data { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; }
    }
    public class ZatcaInvoiceResultResponse
    {
        public ZatcaInvoiceResultResponse()
        {

        }
        public ZatcaInvoiceResultResponse(int statusCode, string? message, bool reportedToZatca,
            List<ValidationResultMessage> warningmessages, List<ValidationResultMessage> errormessages,string invoiceHash="")
        {
            StatusCode = statusCode;
            Message = message ?? GetDefaultMessage(statusCode);
            ReportedToZatca = reportedToZatca;
            WarningMessages = warningmessages;
            ErrorMessages = errormessages;
            InvoiceHash = invoiceHash;
        }

        public int StatusCode { get; set; }
        public string Message { get; set; }
        public bool ReportedToZatca { get; set; }
        public string? InvoiceHash { get; set; }
        public List<ValidationResultMessage>? WarningMessages { get; set; }
        public List<ValidationResultMessage>? ErrorMessages { get; set; }

        private string GetDefaultMessage(int statusCode)
        {
            return statusCode switch
            {
                200 => "Ok, you made it",
                201 => "Created, you made it",
                400 => "bad request, you made",
                401 => "Authorized, you are not authorized",
                403 => "Forbidden, you are not authorized to see this",
                404 => "Resource found, it was not found",
                405 => "Method not allowed, you are not allowed to use this method",
                409 => "Conflict, there is a conflict",
                415 => "Unsupported media type, this media type is not supported",
                422 => "Unprocessable entity, this entity is not processable",
                500 => "Something went wrong and we are going to solve it",
                _ => throw new ArgumentOutOfRangeException("status code", $"Not expected status codes value: {statusCode}"),
            };
        }
    }
    public class ZatcaInvoiceModel
    {
        public string InvoiceHash { get; set; }
        public string QrCode { get; set; }
        public string SignedXml { get; set; }

        public string ReportingStatus { get; set; }
        public string ReportingResult { get; set; }
        public bool IsReportedToZatca { get; set; }
        public DateTime SubmissionDate { get; set; }

        public List<ValidationResultMessage> WarningMessages { get; set; }
        public List<ValidationResultMessage> ErrorMessages { get; set; }

        public string InvoiceBase64 { get; set; }
        public string PreviousInvoiceHash { get; set; }
    }
}
