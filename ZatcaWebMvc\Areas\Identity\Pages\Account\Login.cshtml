﻿@page
@model LoginModel

@{
    ViewData["Title"] = "Sign In";
    Layout = "_Layout";
}

<div class="modern-container" style="min-height: 80vh; display: flex; align-items: center; justify-content: center; padding: 2rem 1rem;">
    <div style="width: 100%; max-width: 400px;">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 2rem;">
            <div style="margin-bottom: 1.5rem;">
                <i class="bi bi-receipt" style="font-size: 3rem; color: var(--color-primary-600);"></i>
            </div>
            <h1 style="font-size: 2rem; font-weight: 700; color: var(--color-gray-900); margin-bottom: 0.5rem;">Welcome back</h1>
            <p style="color: var(--color-gray-600);">Sign in to your ZATCA E-Invoice account</p>
        </div>

        <!-- Login Form Card -->
        <div class="modern-card">
            <form id="account" method="post">
                <div asp-validation-summary="ModelOnly" class="modern-alert modern-alert-error" role="alert"></div>

                <!-- Email Field -->
                <div class="modern-form-group">
                    <div class="modern-floating-input-container">
                        <input asp-for="Input.Email"
                               class="modern-floating-input"
                               type="email"
                               autocomplete="username"
                               aria-required="true"
                               placeholder=" " />
                        <label asp-for="Input.Email" class="modern-floating-label">
                            <i class="bi bi-envelope"></i> Email Address
                        </label>
                    </div>
                    <span asp-validation-for="Input.Email" class="modern-form-error"></span>
                </div>

                <!-- Password Field -->
                <div class="modern-form-group">
                    <div class="modern-floating-input-container">
                        <input asp-for="Input.Password"
                               class="modern-floating-input"
                               type="password"
                               autocomplete="current-password"
                               aria-required="true"
                               placeholder=" " />
                        <label asp-for="Input.Password" class="modern-floating-label">
                            <i class="bi bi-lock"></i> Password
                        </label>
                    </div>
                    <span asp-validation-for="Input.Password" class="modern-form-error"></span>
                </div>

                <!-- Remember Me and Forgot Password -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                    <div class="modern-checkbox-container">
                        <input asp-for="Input.RememberMe" class="modern-checkbox" type="checkbox" id="rememberMe" />
                        <label for="rememberMe" class="modern-checkbox-label">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                    </div>
                    <a id="forgot-password" asp-page="./ForgotPassword" style="font-size: 0.875rem; color: var(--color-primary-600); text-decoration: none;">
                        Forgot password?
                    </a>
                </div>

                <!-- Submit Button -->
                <button id="login-submit" type="submit" class="modern-btn modern-btn-primary" style="width: 100%; margin-bottom: 1.5rem;">
                    <i class="bi bi-box-arrow-in-right"></i>
                    Sign In
                </button>

                <!-- Links -->
                <div style="text-align: center;">
                    <p style="font-size: 0.875rem; color: var(--color-gray-600); margin-bottom: 0.5rem;">
                        Don't have an account?
                        <a asp-page="./Register" asp-route-returnUrl="@Model.ReturnUrl" style="color: var(--color-primary-600); text-decoration: none; font-weight: 500;">
                            Create one here
                        </a>
                    </p>
                    <p style="font-size: 0.75rem; color: var(--color-gray-500);">
                        <a id="resend-confirmation" asp-page="./ResendEmailConfirmation" style="color: var(--color-gray-500); text-decoration: none;">
                            Resend email confirmation
                        </a>
                    </p>
                </div>
            </form>
        </div>

        @if ((Model.ExternalLogins?.Count ?? 0) > 0)
        {
            <!-- External Login Section -->
            <div class="modern-card" style="margin-top: 1.5rem;">
                <div style="text-align: center; margin-bottom: 1rem;">
                    <h3 style="font-size: 1.125rem; font-weight: 500; color: var(--color-gray-900);">Or continue with</h3>
                </div>
                <form id="external-account" asp-page="./ExternalLogin" asp-route-returnUrl="@Model.ReturnUrl" method="post">
                    @foreach (var provider in Model.ExternalLogins!)
                    {
                        <button type="submit"
                                class="modern-btn modern-btn-secondary"
                                style="width: 100%; margin-bottom: 0.75rem;"
                                name="provider"
                                value="@provider.Name"
                                title="Log in using your @provider.DisplayName account">
                            @provider.DisplayName
                        </button>
                    }
                </form>
            </div>
        }

        <!-- Footer -->
        <div style="text-align: center; margin-top: 1.5rem;">
            <p style="font-size: 0.75rem; color: var(--color-gray-500);">
                By signing in, you agree to our terms of service and privacy policy.
            </p>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
