﻿@page
@model LoginModel

@{
    ViewData["Title"] = "Sign In";
    Layout = "_Layout";
}

<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="modern-brand-icon mx-auto mb-6">
                <i class="bi bi-receipt text-primary-600" style="font-size: 3rem;"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Welcome back</h1>
            <p class="text-gray-600">Sign in to your ZATCA E-Invoice account</p>
        </div>

        <!-- Login Form Card -->
        <div class="modern-card">
            <form id="account" method="post" class="space-y-6">
                <div asp-validation-summary="ModelOnly" class="modern-alert modern-alert-error" role="alert" style="display: none;"></div>

                <!-- Email Field -->
                <div class="modern-form-group">
                    <div class="modern-floating-input-container">
                        <input asp-for="Input.Email"
                               class="modern-floating-input"
                               type="email"
                               autocomplete="username"
                               aria-required="true"
                               placeholder=" " />
                        <label asp-for="Input.Email" class="modern-floating-label">
                            <i class="bi bi-envelope me-2"></i>Email Address
                        </label>
                    </div>
                    <span asp-validation-for="Input.Email" class="modern-form-error"></span>
                </div>

                <!-- Password Field -->
                <div class="modern-form-group">
                    <div class="modern-floating-input-container">
                        <input asp-for="Input.Password"
                               class="modern-floating-input"
                               type="password"
                               autocomplete="current-password"
                               aria-required="true"
                               placeholder=" " />
                        <label asp-for="Input.Password" class="modern-floating-label">
                            <i class="bi bi-lock me-2"></i>Password
                        </label>
                    </div>
                    <span asp-validation-for="Input.Password" class="modern-form-error"></span>
                </div>

                <!-- Remember Me -->
                <div class="flex items-center justify-between">
                    <div class="modern-checkbox-container">
                        <input asp-for="Input.RememberMe" class="modern-checkbox" type="checkbox" />
                        <label asp-for="Input.RememberMe" class="modern-checkbox-label">
                            Remember me
                        </label>
                    </div>
                    <a id="forgot-password" asp-page="./ForgotPassword" class="text-sm text-primary-600 hover:text-primary-500 font-medium">
                        Forgot password?
                    </a>
                </div>

                <!-- Submit Button -->
                <button id="login-submit" type="submit" class="modern-btn modern-btn-primary w-full modern-btn-lg">
                    <i class="bi bi-box-arrow-in-right me-2"></i>
                    Sign In
                </button>

                <!-- Links -->
                <div class="text-center space-y-2">
                    <p class="text-sm text-gray-600">
                        Don't have an account?
                        <a asp-page="./Register" asp-route-returnUrl="@Model.ReturnUrl" class="text-primary-600 hover:text-primary-500 font-medium">
                            Create one here
                        </a>
                    </p>
                    <p class="text-xs text-gray-500">
                        <a id="resend-confirmation" asp-page="./ResendEmailConfirmation" class="hover:text-gray-700">
                            Resend email confirmation
                        </a>
                    </p>
                </div>
            </form>
        </div>

        @if ((Model.ExternalLogins?.Count ?? 0) > 0)
        {
            <!-- External Login Section -->
            <div class="modern-card">
                <div class="text-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Or continue with</h3>
                </div>
                <form id="external-account" asp-page="./ExternalLogin" asp-route-returnUrl="@Model.ReturnUrl" method="post">
                    <div class="space-y-3">
                        @foreach (var provider in Model.ExternalLogins!)
                        {
                            <button type="submit"
                                    class="modern-btn modern-btn-secondary w-full"
                                    name="provider"
                                    value="@provider.Name"
                                    title="Log in using your @provider.DisplayName account">
                                @provider.DisplayName
                            </button>
                        }
                    </div>
                </form>
            </div>
        }

        <!-- Footer -->
        <div class="text-center">
            <p class="text-xs text-gray-500">
                By signing in, you agree to our terms of service and privacy policy.
            </p>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
