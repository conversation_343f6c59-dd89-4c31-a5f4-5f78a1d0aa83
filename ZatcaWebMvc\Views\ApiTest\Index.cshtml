@{
    ViewData["Title"] = "API Service Test";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-gear me-2"></i>API Service Test
                    </h1>
                    <p class="text-muted mb-0">Test ZATCA API service connectivity and functionality</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="runAllTests()">
                        <i class="bi bi-play-circle me-2"></i>Run All Tests
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Results -->
    <div class="row g-4">
        <!-- Basic Health Check -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-heart-pulse me-2"></i>Basic Health Check
                    </h5>
                </div>
                <div class="card-body">
                    <div id="health-result">
                        @if (ViewBag.IsHealthy != null)
                        {
                            if ((bool)ViewBag.IsHealthy)
                            {
                                <div class="alert alert-success">
                                    <i class="bi bi-check-circle me-2"></i>API is healthy and accessible
                                </div>
                            }
                            else
                            {
                                <div class="alert alert-danger">
                                    <i class="bi bi-x-circle me-2"></i>API is not accessible
                                </div>
                            }
                        }
                        else
                        {
                            <div class="alert alert-secondary">
                                <i class="bi bi-clock me-2"></i>Click "Test Health" to check API status
                            </div>
                        }
                    </div>
                    <button class="btn btn-outline-primary" onclick="testMethod('health')">
                        <i class="bi bi-arrow-clockwise me-2"></i>Test Health
                    </button>
                </div>
            </div>
        </div>

        <!-- Detailed Health Status -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Detailed Health Status
                    </h5>
                </div>
                <div class="card-body">
                    <div id="healthStatus-result">
                        @if (ViewBag.HealthStatus != null)
                        {
                            <pre class="bg-light p-3 rounded small">@Html.Raw(System.Text.Json.JsonSerializer.Serialize(ViewBag.HealthStatus, new System.Text.Json.JsonSerializerOptions { WriteIndented = true }))</pre>
                        }
                        else
                        {
                            <div class="alert alert-secondary">
                                <i class="bi bi-clock me-2"></i>Click "Test Health Status" to get detailed information
                            </div>
                        }
                    </div>
                    <button class="btn btn-outline-info" onclick="testMethod('healthStatus')">
                        <i class="bi bi-arrow-clockwise me-2"></i>Test Health Status
                    </button>
                </div>
            </div>
        </div>

        <!-- Authentication Test -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-shield-check me-2"></i>Authentication Test
                    </h5>
                </div>
                <div class="card-body">
                    <div id="auth-result">
                        @if (ViewBag.AuthTest != null)
                        {
                            <pre class="bg-light p-3 rounded small">@Html.Raw(System.Text.Json.JsonSerializer.Serialize(ViewBag.AuthTest, new System.Text.Json.JsonSerializerOptions { WriteIndented = true }))</pre>
                        }
                        else
                        {
                            <div class="alert alert-secondary">
                                <i class="bi bi-clock me-2"></i>Click "Test Authentication" to verify API credentials
                            </div>
                        }
                    </div>
                    <button class="btn btn-outline-warning" onclick="testMethod('auth')">
                        <i class="bi bi-arrow-clockwise me-2"></i>Test Authentication
                    </button>
                </div>
            </div>
        </div>

        <!-- API Endpoints Test -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-api me-2"></i>API Endpoints Test
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-success" onclick="testMethod('invoices')">
                            <i class="bi bi-file-earmark-text me-2"></i>Test Invoices API
                        </button>
                        <button class="btn btn-outline-info" onclick="testMethod('certificates')">
                            <i class="bi bi-shield me-2"></i>Test Certificates API
                        </button>
                        <button class="btn btn-outline-primary" onclick="testMethod('sellers')">
                            <i class="bi bi-building me-2"></i>Test Sellers API
                        </button>
                    </div>
                    <div id="endpoints-result" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Display -->
    @if (ViewBag.Error != null)
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-danger">
                    <h5><i class="bi bi-exclamation-triangle me-2"></i>Error</h5>
                    <p class="mb-0">@ViewBag.Error</p>
                </div>
            </div>
        </div>
    }
</div>

<script>
    async function testMethod(method) {
        const resultDiv = document.getElementById(method + '-result') || document.getElementById('endpoints-result');
        
        // Show loading
        resultDiv.innerHTML = '<div class="alert alert-info"><i class="bi bi-hourglass-split me-2"></i>Testing...</div>';
        
        try {
            const response = await fetch('/ApiTest/TestMethod', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'method=' + encodeURIComponent(method)
            });
            
            const result = await response.json();
            
            if (result.success) {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle me-2"></i>Test completed successfully
                    </div>
                    <pre class="bg-light p-3 rounded small">${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-x-circle me-2"></i>Test failed: ${result.error}
                    </div>
                `;
            }
        } catch (error) {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-x-circle me-2"></i>Error: ${error.message}
                </div>
            `;
        }
    }
    
    async function runAllTests() {
        const tests = ['health', 'healthStatus', 'auth', 'invoices', 'certificates', 'sellers'];
        
        for (const test of tests) {
            await testMethod(test);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
        }
    }
</script>
