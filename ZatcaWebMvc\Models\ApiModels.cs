using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace ZatcaWebMvc.Models
{
    #region Identity Models

    /// <summary>
    /// Extended ApplicationUser with additional properties
    /// </summary>
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [StringLength(200)]
        public string? CompanyName { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? LastLoginAt { get; set; }

        public string FullName => $"{FirstName} {LastName}";
    }

    #endregion

    #region Dashboard Models

    /// <summary>
    /// Dashboard statistics view model
    /// </summary>
    public class DashboardViewModel
    {
        public int TotalInvoices { get; set; }
        public int PendingInvoices { get; set; }
        public int SubmittedInvoices { get; set; }
        public int TotalCertificates { get; set; }
        public int TotalSellers { get; set; }
        public decimal TotalRevenue { get; set; }
        public List<InvoiceSubmissionResponse> RecentInvoices { get; set; } = new();
        public bool ApiHealthy { get; set; }
    }

    #endregion

    #region API Response Models

    /// <summary>
    /// Generic API response wrapper
    /// </summary>
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public int StatusCode { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Non-generic API response
    /// </summary>
    public class ApiResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int StatusCode { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }

    #endregion

    #region Invoice Models

    /// <summary>
    /// Invoice submission response model
    /// </summary>
    public class InvoiceSubmissionResponse
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public DateTime IssueDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string InvoiceHash { get; set; } = string.Empty;
        public string QrCode { get; set; } = string.Empty;
        public bool ReportedToZatca { get; set; }
        public string ReportingStatus { get; set; } = string.Empty;
        public string ClearanceStatus { get; set; } = string.Empty;
        public string ValidationResults { get; set; } = string.Empty;
        public DateTime SubmissionDate { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    #endregion

    #region Certificate Models

    /// <summary>
    /// Certificate creation response model
    /// </summary>
    public class CertificateCreationResponse
    {
        public string Csr { get; set; } = string.Empty;
        public string PrivateKey { get; set; } = string.Empty;
        public string BinarySecurityToken { get; set; } = string.Empty;
        public string Secret { get; set; } = string.Empty;
        public long RequestId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    #endregion

    #region Seller Models

    /// <summary>
    /// Seller response model
    /// </summary>
    public class SellerResponse
    {
        public int SellerId { get; set; }
        public string SellerTRN { get; set; } = string.Empty;
        public string SellerName { get; set; } = string.Empty;
        public string StreetName { get; set; } = string.Empty;
        public string CityName { get; set; } = string.Empty;
        public string DistrictName { get; set; } = string.Empty;
        public string BuildingNumber { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public string AdditionalStreetAddress { get; set; } = string.Empty;
        public string IdentityType { get; set; } = string.Empty;
        public string IdentityNumber { get; set; } = string.Empty;
        public string CountryCode { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string BusinessCategory { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }

    /// <summary>
    /// Invoice creation request model
    /// </summary>
    public class InvoiceCreateRequest
    {
        [Required]
        [Display(Name = "Invoice Number")]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Customer Name")]
        public string CustomerName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Total Amount")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public decimal TotalAmount { get; set; }

        [Required]
        [Display(Name = "VAT Amount")]
        [Range(0, double.MaxValue, ErrorMessage = "VAT amount must be 0 or greater")]
        public decimal VatAmount { get; set; }

        [Required]
        [Display(Name = "Invoice Date")]
        [DataType(DataType.Date)]
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        [Display(Name = "Description")]
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// Certificate creation request model
    /// </summary>
    public class CertificateCreateRequest
    {
        [Required]
        [Display(Name = "Business Category")]
        public string BusinessCategory { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Invoice Type")]
        public string InvoiceType { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Location Address")]
        public string LocationAddress { get; set; } = string.Empty;

        [Required]
        [RegularExpression("^3\\d{13}3$", ErrorMessage = "VAT Number must be a 15-digit number starting and ending with 3")]
        [Display(Name = "VAT Number")]
        public string VATNumber { get; set; } = string.Empty;

        [Display(Name = "Country Name")]
        public string CountryName { get; set; } = "SA";

        [Required]
        [Display(Name = "Branch Name")]
        public string BranchName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Organization Name")]
        public string OrganizationName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [Display(Name = "Email")]
        public string Email { get; set; } = string.Empty;

        [Required]
        [Display(Name = "OTP")]
        public string Otp { get; set; } = string.Empty;

        [Display(Name = "Is Production")]
        public bool IsProduction { get; set; } = false;
    }

    /// <summary>
    /// Seller creation request model
    /// </summary>
    public class SellerCreateRequest
    {
        [Required]
        [RegularExpression("^3\\d{13}3$", ErrorMessage = "TRN must be a 15-digit number starting and ending with 3")]
        [Display(Name = "Seller TRN")]
        public string SellerTRN { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Seller Name")]
        public string SellerName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Street Name")]
        public string StreetName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "City Name")]
        public string CityName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "District Name")]
        public string DistrictName { get; set; } = string.Empty;

        [Required]
        [RegularExpression("^\\d{4}$", ErrorMessage = "Building Number must be a 4-digit number")]
        [Display(Name = "Building Number")]
        public string BuildingNumber { get; set; } = string.Empty;

        [Display(Name = "Postal Code")]
        public string PostalCode { get; set; } = string.Empty;

        [Display(Name = "Additional Street Address")]
        public string AdditionalStreetAddress { get; set; } = string.Empty;

        [Display(Name = "Identity Type")]
        public string IdentityType { get; set; } = "CRN";

        [Required]
        [Display(Name = "Identity Number")]
        public string IdentityNumber { get; set; } = string.Empty;

        [Display(Name = "Country Code")]
        public string CountryCode { get; set; } = "SA";

        [EmailAddress]
        [Display(Name = "Email")]
        public string Email { get; set; } = string.Empty;

        [Display(Name = "Phone")]
        public string Phone { get; set; } = string.Empty;

        [Display(Name = "Business Category")]
        public string BusinessCategory { get; set; } = string.Empty;
    }

    #endregion

    #region View Models

    /// <summary>
    /// Invoice line item for creating invoices
    /// </summary>
    public class InvoiceLineItem
    {
        [Required]
        [Display(Name = "Description")]
        public string Description { get; set; } = string.Empty;

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Quantity must be at least 1")]
        [Display(Name = "Quantity")]
        public int Quantity { get; set; } = 1;

        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Unit price must be greater than 0")]
        [Display(Name = "Unit Price")]
        [DataType(DataType.Currency)]
        public decimal UnitPrice { get; set; }

        [Range(0, 100, ErrorMessage = "Tax rate must be between 0 and 100")]
        [Display(Name = "Tax Rate (%)")]
        public decimal TaxRate { get; set; } = 15; // Default VAT rate in Saudi Arabia

        public decimal LineTotal => Quantity * UnitPrice;
        public decimal TaxAmount => LineTotal * (TaxRate / 100);
        public decimal LineTotalWithTax => LineTotal + TaxAmount;
    }

    #endregion
}
