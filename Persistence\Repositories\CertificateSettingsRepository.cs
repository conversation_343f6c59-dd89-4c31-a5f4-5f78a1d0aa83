﻿using Application.Contracts.IRepository;
using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Persistence.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Persistence.Repositories
{
    public class CertificateSettingsRepository : ICertificateSettingsRepository
    {
        private readonly ApplicationDbContext _context;

        public CertificateSettingsRepository(ApplicationDbContext context)
        {
            _context = context;
        }
       
        /// <summary>
        /// Saves the CSR and private key asynchronously.
        /// </summary>
        /// <param name="csr">The Certificate Signing Request (CSR) as a string.</param>
        /// <param name="privateKey">The private key as a string.</param>
        /// <returns>Returns a <see cref="Guid"/> representing the ID of the newly saved CSR and private key.</returns>
        public async Task<Guid> SaveCsrAndPrivateKeyAsync(string csr, string privateKey)
        {
            var newCsr = new Domain.Entities.CertificateSettings
            {
                Csr = csr,
                PrivateKey = privateKey
            };

            await _context.EI_CertificateSettings.AddAsync(newCsr);
           
            await _context.SaveChangesAsync();

            return newCsr.Id;
        }

        /// <summary>
        /// Retrieves the certificate settings by ID asynchronously.
        /// </summary>
        /// <param name="id">The ID of the certificate settings as a string.</param>
        /// <returns>Returns the <see cref="CertificateSettings"/> if found, otherwise null.</returns>
        public async Task<CertificateSettings> GetCertificateByIdAsync(string id)
        {
            return await _context.EI_CertificateSettings.SingleOrDefaultAsync(
                x => x.Id == new Guid(id));
        }

        /// <summary>
        /// Adds new certificate settings asynchronously.
        /// </summary>
        /// <param name="certificate">The <see cref="CertificateSettings"/> object to be added.</param>
        /// <returns>Returns the added <see cref="CertificateSettings"/> object.</returns>
        public async Task<CertificateSettings> AddAsync(CertificateSettings certificate)
        {
            await _context.EI_CertificateSettings.AddAsync(certificate);
            await _context.SaveChangesAsync();

            return certificate;
        }
        /// <summary>
        /// Adds new certificate settings asynchronously.
        /// </summary>
        /// <param name="certificate">The <see cref="CertificateSettings"/> object to be added.</param>
        /// <returns>Returns the added <see cref="CertificateSettings"/> object.</returns>
        public async Task RemoveAsync(CertificateSettings certificate)
        {
            _context.EI_CertificateSettings.Remove(certificate);
            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// Retrieves the first certificate settings.
        /// </summary>
        /// <returns>Returns the first <see cref="CertificateSettings"/> object found.</returns>
        public CertificateSettings GetCertificateSettings()
        {
            return _context.EI_CertificateSettings.FirstOrDefault();
        }

        /// <summary>
        /// Retrieves the list of certificate settings.
        /// </summary>
        /// <returns>Returns List of  <see cref="CertificateSettings"/> object found.</returns>

        public async Task<IEnumerable<CertificateSettings>> GetCertificatesSettings()
        {
            return await _context.EI_CertificateSettings.ToListAsync();
        }

    }
}
