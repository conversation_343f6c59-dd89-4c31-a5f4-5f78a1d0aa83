using Newtonsoft.Json;
using System.Text;
using ZatcaWebMvc.Models;

namespace ZatcaWebMvc.Services
{
    /// <summary>
    /// Service for communicating with ZATCA API
    /// </summary>
    public class ZatcaApiService : IZatcaApiService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ZatcaApiService> _logger;
        private readonly string _baseUrl;

        public ZatcaApiService(HttpClient httpClient, ILogger<ZatcaApiService> logger, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = configuration["ZatcaApi:BaseUrl"] ?? "http://localhost:5147";
            
            // Configure HttpClient
            _httpClient.BaseAddress = new Uri(_baseUrl);
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
        }

        #region Invoice Operations

        public async Task<ApiResponse<InvoiceSubmissionResponse>> CreateInvoiceAsync(InvoiceCreateRequest request)
        {
            try
            {
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/api/v1/Invoicing", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<InvoiceSubmissionResponse>>(responseContent);
                    return result ?? new ApiResponse<InvoiceSubmissionResponse> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<InvoiceSubmissionResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode,
                    Errors = new List<string> { responseContent }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating invoice");
                return new ApiResponse<InvoiceSubmissionResponse>
                {
                    Success = false,
                    Message = "An error occurred while creating the invoice",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<InvoiceSubmissionResponse>> GetInvoiceAsync(int id)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/v1/Invoicing/{id}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<InvoiceSubmissionResponse>>(responseContent);
                    return result ?? new ApiResponse<InvoiceSubmissionResponse> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<InvoiceSubmissionResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice {InvoiceId}", id);
                return new ApiResponse<InvoiceSubmissionResponse>
                {
                    Success = false,
                    Message = "An error occurred while retrieving the invoice",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<List<InvoiceSubmissionResponse>>> GetAllInvoicesAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/Invoicing");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<List<InvoiceSubmissionResponse>>>(responseContent);
                    return result ?? new ApiResponse<List<InvoiceSubmissionResponse>> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<List<InvoiceSubmissionResponse>>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all invoices");
                return new ApiResponse<List<InvoiceSubmissionResponse>>
                {
                    Success = false,
                    Message = "An error occurred while retrieving invoices",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        #endregion

        #region Certificate Operations

        public async Task<ApiResponse<CertificateCreationResponse>> CreateCertificateAsync(CertificateCreateRequest request)
        {
            try
            {
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/api/v1/Certificate", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<CertificateCreationResponse>>(responseContent);
                    return result ?? new ApiResponse<CertificateCreationResponse> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<CertificateCreationResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode,
                    Errors = new List<string> { responseContent }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating certificate");
                return new ApiResponse<CertificateCreationResponse>
                {
                    Success = false,
                    Message = "An error occurred while creating the certificate",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<List<CertificateCreationResponse>>> GetAllCertificatesAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/Certificate");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<List<CertificateCreationResponse>>>(responseContent);
                    return result ?? new ApiResponse<List<CertificateCreationResponse>> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<List<CertificateCreationResponse>>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all certificates");
                return new ApiResponse<List<CertificateCreationResponse>>
                {
                    Success = false,
                    Message = "An error occurred while retrieving certificates",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<CertificateCreationResponse>> GetCertificateAsync(int id)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/v1/Certificate/{id}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<CertificateCreationResponse>>(responseContent);
                    return result ?? new ApiResponse<CertificateCreationResponse> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<CertificateCreationResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting certificate {CertificateId}", id);
                return new ApiResponse<CertificateCreationResponse>
                {
                    Success = false,
                    Message = "An error occurred while retrieving the certificate",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        #endregion

        #region Seller Operations

        public async Task<ApiResponse<SellerResponse>> CreateSellerAsync(SellerCreateRequest request)
        {
            try
            {
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/api/v1/Seller", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<SellerResponse>>(responseContent);
                    return result ?? new ApiResponse<SellerResponse> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<SellerResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode,
                    Errors = new List<string> { responseContent }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating seller");
                return new ApiResponse<SellerResponse>
                {
                    Success = false,
                    Message = "An error occurred while creating the seller",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<List<SellerResponse>>> GetAllSellersAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/Seller");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<List<SellerResponse>>>(responseContent);
                    return result ?? new ApiResponse<List<SellerResponse>> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<List<SellerResponse>>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all sellers");
                return new ApiResponse<List<SellerResponse>>
                {
                    Success = false,
                    Message = "An error occurred while retrieving sellers",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<SellerResponse>> GetSellerAsync(int id)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/v1/Seller/{id}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<SellerResponse>>(responseContent);
                    return result ?? new ApiResponse<SellerResponse> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<SellerResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting seller {SellerId}", id);
                return new ApiResponse<SellerResponse>
                {
                    Success = false,
                    Message = "An error occurred while retrieving the seller",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse> UpdateSellerAsync(int id, SellerCreateRequest request)
        {
            try
            {
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PutAsync($"/api/v1/Seller/{id}", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse>(responseContent);
                    return result ?? new ApiResponse { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode,
                    Errors = new List<string> { responseContent }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating seller {SellerId}", id);
                return new ApiResponse
                {
                    Success = false,
                    Message = "An error occurred while updating the seller",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse> DeleteSellerAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"/api/v1/Seller/{id}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse>(responseContent);
                    return result ?? new ApiResponse { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting seller {SellerId}", id);
                return new ApiResponse
                {
                    Success = false,
                    Message = "An error occurred while deleting the seller",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        #endregion

        #region Health Check

        public async Task<bool> IsApiHealthyAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/swagger/index.html");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
