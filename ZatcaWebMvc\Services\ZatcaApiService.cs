using Newtonsoft.Json;
using System.Net;
using System.Text;
using ZatcaWebMvc.Models;

namespace ZatcaWebMvc.Services
{
    /// <summary>
    /// Enhanced service for communicating with ZATCA API with authentication and error handling
    /// </summary>
    public class ZatcaApiService : IZatcaApiService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ZatcaApiService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _baseUrl;
        private readonly string? _apiKey;
        private readonly int _timeoutSeconds;

        public ZatcaApiService(HttpClient httpClient, ILogger<ZatcaApiService> logger, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _configuration = configuration;
            _baseUrl = configuration["ZatcaApi:BaseUrl"] ?? "http://localhost:5147";
            _apiKey = configuration["ZatcaApi:ApiKey"];
            _timeoutSeconds = configuration.GetValue<int>("ZatcaApi:TimeoutSeconds", 30);

            ConfigureHttpClient();
        }

        /// <summary>
        /// Configure HTTP client with authentication and default settings
        /// </summary>
        private void ConfigureHttpClient()
        {
            _httpClient.BaseAddress = new Uri(_baseUrl);
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "ZATCA-MVC-Client/1.0");
            _httpClient.Timeout = TimeSpan.FromSeconds(_timeoutSeconds);

            // Add API key if configured
            if (!string.IsNullOrEmpty(_apiKey))
            {
                _httpClient.DefaultRequestHeaders.Add("X-API-Key", _apiKey);
            }

            _logger.LogInformation("ZATCA API Service configured with base URL: {BaseUrl}", _baseUrl);
        }

        #region Private Helper Methods

        /// <summary>
        /// Handle HTTP response and create standardized API response
        /// </summary>
        private async Task<ApiResponse<T>> HandleResponseAsync<T>(HttpResponseMessage response, string operation)
        {
            var responseContent = await response.Content.ReadAsStringAsync();

            try
            {
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<T>>(responseContent);
                    if (result != null)
                    {
                        _logger.LogInformation("API operation {Operation} completed successfully", operation);
                        return result;
                    }

                    // If direct deserialization fails, try to wrap the response
                    var data = JsonConvert.DeserializeObject<T>(responseContent);
                    return new ApiResponse<T>
                    {
                        Success = true,
                        Data = data,
                        Message = "Operation completed successfully",
                        StatusCode = (int)response.StatusCode
                    };
                }

                // Handle error responses
                var errorResponse = new ApiResponse<T>
                {
                    Success = false,
                    Message = GetErrorMessage(response.StatusCode, operation),
                    StatusCode = (int)response.StatusCode,
                    Errors = new List<string>()
                };

                // Try to extract error details from response
                try
                {
                    var errorData = JsonConvert.DeserializeObject<ApiResponse>(responseContent);
                    if (errorData?.Errors?.Any() == true)
                    {
                        errorResponse.Errors = errorData.Errors;
                    }
                    if (!string.IsNullOrEmpty(errorData?.Message))
                    {
                        errorResponse.Message = errorData.Message;
                    }
                }
                catch
                {
                    errorResponse.Errors.Add(responseContent);
                }

                _logger.LogWarning("API operation {Operation} failed with status {StatusCode}: {Message}",
                    operation, response.StatusCode, errorResponse.Message);

                return errorResponse;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Failed to deserialize API response for operation {Operation}", operation);
                return new ApiResponse<T>
                {
                    Success = false,
                    Message = "Failed to process API response",
                    StatusCode = (int)response.StatusCode,
                    Errors = new List<string> { ex.Message, responseContent }
                };
            }
        }

        /// <summary>
        /// Get user-friendly error message based on HTTP status code
        /// </summary>
        private static string GetErrorMessage(HttpStatusCode statusCode, string operation)
        {
            return statusCode switch
            {
                HttpStatusCode.Unauthorized => "Authentication failed. Please check your API credentials.",
                HttpStatusCode.Forbidden => "Access denied. You don't have permission to perform this operation.",
                HttpStatusCode.NotFound => $"The requested resource for {operation} was not found.",
                HttpStatusCode.BadRequest => "Invalid request data. Please check your input and try again.",
                HttpStatusCode.InternalServerError => "Server error occurred. Please try again later.",
                HttpStatusCode.ServiceUnavailable => "ZATCA API service is temporarily unavailable.",
                HttpStatusCode.RequestTimeout => "Request timed out. Please try again.",
                _ => $"API operation {operation} failed with status {statusCode}"
            };
        }

        /// <summary>
        /// Create HTTP content from object
        /// </summary>
        private static StringContent CreateJsonContent(object data)
        {
            var json = JsonConvert.SerializeObject(data, new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
                DateFormatHandling = DateFormatHandling.IsoDateFormat
            });
            return new StringContent(json, Encoding.UTF8, "application/json");
        }

        /// <summary>
        /// Execute API operation with comprehensive error handling
        /// </summary>
        private async Task<ApiResponse<T>> ExecuteApiOperationAsync<T>(
            Func<Task<HttpResponseMessage>> operation,
            string operationName)
        {
            try
            {
                _logger.LogInformation("Executing API operation: {Operation}", operationName);
                var response = await operation();
                return await HandleResponseAsync<T>(response, operationName);
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP request failed for operation {Operation}", operationName);
                return new ApiResponse<T>
                {
                    Success = false,
                    Message = "Network error occurred. Please check your connection and try again.",
                    Errors = new List<string> { ex.Message }
                };
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                _logger.LogError(ex, "Request timeout for operation {Operation}", operationName);
                return new ApiResponse<T>
                {
                    Success = false,
                    Message = "Request timed out. Please try again.",
                    Errors = new List<string> { "Operation timed out" }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in operation {Operation}", operationName);
                return new ApiResponse<T>
                {
                    Success = false,
                    Message = "An unexpected error occurred. Please try again.",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        #endregion

        #region Invoice Operations

        public async Task<ApiResponse<InvoiceSubmissionResponse>> CreateInvoiceAsync(InvoiceCreateRequest request)
        {
            if (request == null)
            {
                return new ApiResponse<InvoiceSubmissionResponse>
                {
                    Success = false,
                    Message = "Invoice request cannot be null",
                    Errors = new List<string> { "Invalid request data" }
                };
            }

            return await ExecuteApiOperationAsync<InvoiceSubmissionResponse>(
                async () =>
                {
                    var content = CreateJsonContent(request);
                    return await _httpClient.PostAsync("/api/v1/Invoicing", content);
                },
                "CreateInvoice"
            );
        }

        public async Task<ApiResponse<InvoiceSubmissionResponse>> GetInvoiceAsync(int id)
        {
            if (id <= 0)
            {
                return new ApiResponse<InvoiceSubmissionResponse>
                {
                    Success = false,
                    Message = "Invalid invoice ID",
                    Errors = new List<string> { "Invoice ID must be greater than 0" }
                };
            }

            return await ExecuteApiOperationAsync<InvoiceSubmissionResponse>(
                async () => await _httpClient.GetAsync($"/api/v1/Invoicing/{id}"),
                $"GetInvoice-{id}"
            );
        }

        public async Task<ApiResponse<List<InvoiceSubmissionResponse>>> GetAllInvoicesAsync()
        {
            return await ExecuteApiOperationAsync<List<InvoiceSubmissionResponse>>(
                async () => await _httpClient.GetAsync("/api/v1/Invoicing"),
                "GetAllInvoices"
            );
        }

        #endregion

        #region Certificate Operations

        public async Task<ApiResponse<CertificateCreationResponse>> CreateCertificateAsync(CertificateCreateRequest request)
        {
            try
            {
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/api/v1/Certificate", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<CertificateCreationResponse>>(responseContent);
                    return result ?? new ApiResponse<CertificateCreationResponse> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<CertificateCreationResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode,
                    Errors = new List<string> { responseContent }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating certificate");
                return new ApiResponse<CertificateCreationResponse>
                {
                    Success = false,
                    Message = "An error occurred while creating the certificate",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<List<CertificateCreationResponse>>> GetAllCertificatesAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/Certificate");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<List<CertificateCreationResponse>>>(responseContent);
                    return result ?? new ApiResponse<List<CertificateCreationResponse>> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<List<CertificateCreationResponse>>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all certificates");
                return new ApiResponse<List<CertificateCreationResponse>>
                {
                    Success = false,
                    Message = "An error occurred while retrieving certificates",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<CertificateCreationResponse>> GetCertificateAsync(int id)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/v1/Certificate/{id}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<CertificateCreationResponse>>(responseContent);
                    return result ?? new ApiResponse<CertificateCreationResponse> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<CertificateCreationResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting certificate {CertificateId}", id);
                return new ApiResponse<CertificateCreationResponse>
                {
                    Success = false,
                    Message = "An error occurred while retrieving the certificate",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        #endregion

        #region Seller Operations

        public async Task<ApiResponse<SellerResponse>> CreateSellerAsync(SellerCreateRequest request)
        {
            try
            {
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/api/v1/Seller", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<SellerResponse>>(responseContent);
                    return result ?? new ApiResponse<SellerResponse> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<SellerResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode,
                    Errors = new List<string> { responseContent }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating seller");
                return new ApiResponse<SellerResponse>
                {
                    Success = false,
                    Message = "An error occurred while creating the seller",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<List<SellerResponse>>> GetAllSellersAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/Seller");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<List<SellerResponse>>>(responseContent);
                    return result ?? new ApiResponse<List<SellerResponse>> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<List<SellerResponse>>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all sellers");
                return new ApiResponse<List<SellerResponse>>
                {
                    Success = false,
                    Message = "An error occurred while retrieving sellers",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse<SellerResponse>> GetSellerAsync(int id)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/v1/Seller/{id}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<SellerResponse>>(responseContent);
                    return result ?? new ApiResponse<SellerResponse> { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse<SellerResponse>
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting seller {SellerId}", id);
                return new ApiResponse<SellerResponse>
                {
                    Success = false,
                    Message = "An error occurred while retrieving the seller",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse> UpdateSellerAsync(int id, SellerCreateRequest request)
        {
            try
            {
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PutAsync($"/api/v1/Seller/{id}", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse>(responseContent);
                    return result ?? new ApiResponse { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode,
                    Errors = new List<string> { responseContent }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating seller {SellerId}", id);
                return new ApiResponse
                {
                    Success = false,
                    Message = "An error occurred while updating the seller",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse> DeleteSellerAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"/api/v1/Seller/{id}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse>(responseContent);
                    return result ?? new ApiResponse { Success = false, Message = "Failed to deserialize response" };
                }
                
                return new ApiResponse
                {
                    Success = false,
                    Message = $"API call failed with status {response.StatusCode}",
                    StatusCode = (int)response.StatusCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting seller {SellerId}", id);
                return new ApiResponse
                {
                    Success = false,
                    Message = "An error occurred while deleting the seller",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        #endregion

        #region Health Check

        public async Task<bool> IsApiHealthyAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/swagger/index.html");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
