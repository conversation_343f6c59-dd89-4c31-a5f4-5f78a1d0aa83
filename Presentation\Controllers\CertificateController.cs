﻿using Application.Contracts.IServices;
using Application.Contracts.Zatca;
using Application.Dtos.Requests;
using Application.Models.Zatca;
using Domain.Entities;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using NuGet.Protocol;
using Presentation.Models;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace Presentation.Controllers
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    [ApiVersion("1.0")]
    public class CertificateController : ControllerBase
    {
        private readonly ICertificateCreationService _certificateCreationService;
        private readonly ICertificateConfiguration _certificateConfiguration;
        private readonly IConfiguration _config;
        public CertificateController(ICertificateConfiguration certificateConfiguration, ICertificateCreationService certificateCreation, IConfiguration config)
        {
            _certificateConfiguration = certificateConfiguration;
            _certificateCreationService = certificateCreation;
            _config = config;
        }
        // GET: api/<CertificateController>
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            IEnumerable<CertificateSettings> certificates = await _certificateConfiguration.GetCertificatesDetails();
            if (certificates.Any())
            {
                return Ok(certificates);
            }
            else
            {
                var response = new
                {
                    StatusCode = 400,
                    Message = "There're no Certificates added",
                };
                return BadRequest(response.ToJson());
            }
        }

        // GET api/<CertificateController>/details/5
        [HttpGet("details/{value}")]
        public async Task<IActionResult> Get(string value)
        {
            CertificateSettings certificateSettings = await _certificateConfiguration.GetCertificateDetails(value);
            if (certificateSettings!=null)
            {
                return Ok(certificateSettings);
            }
            else
            {
                var response = new
                {
                    StatusCode = 404,
                    Message = $"There're no Certificates with id={value}",
                };
                return NotFound(response.ToJson());

            }

        }

        // POST api/<CertificateController>
        [HttpPost]
        public async Task<IActionResult> Post([FromBody] ZatcaCsrCreationRequestDto zatcaCsr,string OTP)
        {
            if (zatcaCsr==null)
            {
                var response = new
                {
                    StatusCode = 422,
                    Message = "The Provided info is not valid",
                };
                return BadRequest(response.ToJson());
            }
            try
            {
                SharedData.APIUrl = _config["ZatcaSettings:ProductionUrl"];
                _config["ZatcaSettings:Otp"] = OTP;
                var result = await _certificateCreationService.CreateCertificateAsync(zatcaCsr);
                if (result.Success)
                {
                    return Ok(result.Data);
                }
                return BadRequest(result.ToJson());

            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
        // DELETE api/<CertificateController>/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAsync(string id)
        {
            CertificateSettings certificateSettings = await _certificateConfiguration.GetCertificateDetails(id);
            if (certificateSettings != null)
            {
                await _certificateConfiguration.RemoveCertificate(certificateSettings);
                var response = new
                {
                    StatusCode = 200,
                    Message = $"The Certificates with id={id} was successfully deleted",
                };
                return Ok(response);
            }
            else
            {
                var response = new
                {
                    StatusCode = 404,
                    Message = $"There're no Certificates with id={id}",
                };
                return NotFound(response.ToJson());

            }
        }
        //Simulation
        // POST api/v1.0/<CertificateController>/Simulation
        [HttpPost("Simulation")]
        public async Task<IActionResult> SimulationPost([FromBody] ZatcaCsrCreationRequestDto zatcaCsr,string OTP)
        {
            if (zatcaCsr == null)
            {
                var response = new
                {
                    StatusCode = 422,
                    Message = "The Provided info is not valid",
                };
                return BadRequest(response.ToJson());
            }
            try
            {
                SharedData.APIUrl = _config["ZatcaSettings:SimulationUrl"];
                _config["ZatcaSettings:Otp"] = OTP;
                var result = await _certificateCreationService.CreateCertificateAsync(zatcaCsr);
                if (result.Success)
                {
                    return Ok(result.Data);
                }
                return BadRequest(result.ToJson());

            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
