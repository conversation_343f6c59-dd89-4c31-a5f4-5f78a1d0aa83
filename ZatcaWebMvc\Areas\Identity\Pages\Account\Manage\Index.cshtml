﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Profile Settings";
    ViewData["ActivePage"] = ManageNavPages.Index;
    Layout = "_Layout";
}

<div class="modern-container py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Profile Settings</h1>
            <p class="text-gray-600">Manage your account information and preferences</p>
        </div>

        <!-- Status Message -->
        <partial name="_StatusMessage" for="StatusMessage" />

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Navigation Sidebar -->
            <div class="lg:col-span-1">
                <partial name="_ManageNav" />
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="modern-card">
                    <div class="modern-card-header">
                        <h2 class="text-xl font-semibold text-gray-900">Personal Information</h2>
                        <p class="text-sm text-gray-600">Update your personal details and contact information</p>
                    </div>

                    <form id="profile-form" method="post" class="space-y-6">
                        <div asp-validation-summary="ModelOnly" class="modern-alert modern-alert-error" role="alert" style="display: none;"></div>

                        <!-- Username (Read-only) -->
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="bi bi-at me-2"></i>Username (Email)
                            </label>
                            <input asp-for="Username"
                                   class="modern-form-input bg-gray-50"
                                   disabled
                                   readonly />
                            <div class="text-xs text-gray-500 mt-1">
                                Your username cannot be changed. Contact support if you need to update your email address.
                            </div>
                        </div>

                        <!-- Name Fields Row -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- First Name -->
                            <div class="modern-form-group">
                                <div class="modern-floating-input-container">
                                    <input asp-for="Input.FirstName"
                                           class="modern-floating-input"
                                           type="text"
                                           autocomplete="given-name"
                                           aria-required="true"
                                           placeholder=" " />
                                    <label asp-for="Input.FirstName" class="modern-floating-label">
                                        <i class="bi bi-person me-2"></i>First Name
                                    </label>
                                </div>
                                <span asp-validation-for="Input.FirstName" class="modern-form-error"></span>
                            </div>

                            <!-- Last Name -->
                            <div class="modern-form-group">
                                <div class="modern-floating-input-container">
                                    <input asp-for="Input.LastName"
                                           class="modern-floating-input"
                                           type="text"
                                           autocomplete="family-name"
                                           aria-required="true"
                                           placeholder=" " />
                                    <label asp-for="Input.LastName" class="modern-floating-label">
                                        <i class="bi bi-person me-2"></i>Last Name
                                    </label>
                                </div>
                                <span asp-validation-for="Input.LastName" class="modern-form-error"></span>
                            </div>
                        </div>

                        <!-- Company Name -->
                        <div class="modern-form-group">
                            <div class="modern-floating-input-container">
                                <input asp-for="Input.CompanyName"
                                       class="modern-floating-input"
                                       type="text"
                                       autocomplete="organization"
                                       placeholder=" " />
                                <label asp-for="Input.CompanyName" class="modern-floating-label">
                                    <i class="bi bi-building me-2"></i>Company Name
                                </label>
                            </div>
                            <span asp-validation-for="Input.CompanyName" class="modern-form-error"></span>
                        </div>

                        <!-- Phone Number -->
                        <div class="modern-form-group">
                            <div class="modern-floating-input-container">
                                <input asp-for="Input.PhoneNumber"
                                       class="modern-floating-input"
                                       type="tel"
                                       autocomplete="tel"
                                       placeholder=" " />
                                <label asp-for="Input.PhoneNumber" class="modern-floating-label">
                                    <i class="bi bi-telephone me-2"></i>Phone Number
                                </label>
                            </div>
                            <span asp-validation-for="Input.PhoneNumber" class="modern-form-error"></span>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end">
                            <button id="update-profile-button" type="submit" class="modern-btn modern-btn-primary">
                                <i class="bi bi-check-circle me-2"></i>
                                Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
