@{
    var size = ViewData["Size"]?.ToString() ?? "md";
    var text = ViewData["Text"]?.ToString() ?? "Loading...";
    var centered = ViewData["Centered"]?.ToString() == "true";
    var overlay = ViewData["Overlay"]?.ToString() == "true";
}

@if (overlay)
{
    <div class="loading-overlay position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background-color: rgba(255,255,255,0.8); z-index: 9999;">
        <div class="text-center">
            <div class="spinner-border text-primary @(size == "sm" ? "spinner-border-sm" : size == "lg" ? "spinner-border-lg" : "")" role="status">
                <span class="visually-hidden">@text</span>
            </div>
            <div class="mt-2 text-muted">@text</div>
        </div>
    </div>
}
else if (centered)
{
    <div class="text-center py-5">
        <div class="spinner-border text-primary @(size == "sm" ? "spinner-border-sm" : size == "lg" ? "spinner-border-lg" : "")" role="status">
            <span class="visually-hidden">@text</span>
        </div>
        <div class="mt-2 text-muted">@text</div>
    </div>
}
else
{
    <div class="d-inline-flex align-items-center">
        <div class="spinner-border text-primary @(size == "sm" ? "spinner-border-sm" : size == "lg" ? "spinner-border-lg" : "")" role="status">
            <span class="visually-hidden">@text</span>
        </div>
        <span class="ms-2 text-muted">@text</span>
    </div>
}

<style>
    .spinner-border-lg {
        width: 3rem;
        height: 3rem;
    }
    
    .loading-overlay {
        backdrop-filter: blur(2px);
    }
</style>
