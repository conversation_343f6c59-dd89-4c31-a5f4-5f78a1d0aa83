@model ZatcaWebMvc.Models.InvoiceSubmissionResponse
@{
    ViewData["Title"] = "Invoice Details";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-action="Index">Invoices</a></li>
                    <li class="breadcrumb-item active">@Model.InvoiceNumber</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="bi bi-receipt me-2"></i>Invoice Details
            </h1>
            <p class="text-muted mb-0">Invoice #@Model.InvoiceNumber</p>
        </div>
        <div>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Back to List
            </a>
            @if (!Model.ReportedToZatca)
            {
                <form asp-action="Submit" method="post" style="display: inline;">
                    <input type="hidden" name="id" value="@Model.Id" />
                    <button type="submit" class="btn btn-success ms-2" 
                            onclick="return confirm('Are you sure you want to submit this invoice to ZATCA?')">
                        <i class="bi bi-send me-1"></i>Submit to ZATCA
                    </button>
                </form>
            }
        </div>
    </div>

    <!-- Success Message -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="row">
        <!-- Invoice Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-info-circle me-1"></i>Invoice Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Invoice Number:</td>
                                    <td>@Model.InvoiceNumber</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Customer Name:</td>
                                    <td>@Model.CustomerName</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Issue Date:</td>
                                    <td>@Model.IssueDate.ToString("dd/MM/yyyy")</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Created At:</td>
                                    <td>@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Net Amount:</td>
                                    <td class="text-success fw-bold">@((Model.TotalAmount - Model.TaxAmount).ToString("C"))</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">VAT Amount:</td>
                                    <td>@Model.TaxAmount.ToString("C")</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Total Amount:</td>
                                    <td class="text-success fw-bold h5">@Model.TotalAmount.ToString("C")</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ZATCA Technical Details -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-gear me-1"></i>ZATCA Technical Details
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Invoice Hash:</label>
                                <div class="input-group">
                                    <input type="text" class="form-control font-monospace" 
                                           value="@Model.InvoiceHash" readonly />
                                    <button class="btn btn-outline-secondary" type="button" 
                                            onclick="copyToClipboard('@Model.InvoiceHash')" title="Copy">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Submission Date:</label>
                                <input type="text" class="form-control" 
                                       value="@(Model.SubmissionDate != DateTime.MinValue ? Model.SubmissionDate.ToString("dd/MM/yyyy HH:mm") : "Not submitted")" 
                                       readonly />
                            </div>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.QrCode))
                    {
                        <div class="mb-3">
                            <label class="form-label fw-bold">QR Code:</label>
                            <div class="input-group">
                                <textarea class="form-control font-monospace" rows="3" readonly>@Model.QrCode</textarea>
                                <button class="btn btn-outline-secondary" type="button" 
                                        onclick="copyToClipboard('@Model.QrCode')" title="Copy">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </div>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.ValidationResults))
                    {
                        <div class="mb-3">
                            <label class="form-label fw-bold">Validation Results:</label>
                            <textarea class="form-control font-monospace" rows="4" readonly>@Model.ValidationResults</textarea>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Status and Actions -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-flag me-1"></i>Status Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Invoice Status:</label>
                        @{
                            var statusClass = Model.Status switch
                            {
                                "Draft" => "bg-secondary",
                                "Submitted" => "bg-info",
                                "Approved" => "bg-success",
                                "Rejected" => "bg-danger",
                                _ => "bg-secondary"
                            };
                        }
                        <div>
                            <span class="badge @statusClass fs-6">@Model.Status</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">ZATCA Reporting:</label>
                        <div>
                            @if (Model.ReportedToZatca)
                            {
                                <span class="badge bg-success fs-6">
                                    <i class="bi bi-check-circle me-1"></i>Reported
                                </span>
                            }
                            else
                            {
                                <span class="badge bg-warning fs-6">
                                    <i class="bi bi-clock me-1"></i>Pending
                                </span>
                            }
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.ReportingStatus))
                    {
                        <div class="mb-3">
                            <label class="form-label fw-bold">Reporting Status:</label>
                            <div>
                                <span class="badge bg-info fs-6">@Model.ReportingStatus</span>
                            </div>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.ClearanceStatus))
                    {
                        <div class="mb-3">
                            <label class="form-label fw-bold">Clearance Status:</label>
                            <div>
                                <span class="badge bg-info fs-6">@Model.ClearanceStatus</span>
                            </div>
                        </div>
                    }

                    <hr>

                    <div class="d-grid gap-2">
                        @if (!Model.ReportedToZatca)
                        {
                            <button type="button" class="btn btn-success" id="submitBtn">
                                <i class="bi bi-send me-1"></i>Submit to ZATCA
                            </button>
                        }
                        <button type="button" class="btn btn-outline-primary" onclick="refreshStatus()">
                            <i class="bi bi-arrow-clockwise me-1"></i>Refresh Status
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show success message
                const toast = document.createElement('div');
                toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
                toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="bi bi-check-circle me-2"></i>Copied to clipboard!
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                `;
                document.body.appendChild(toast);
                const bsToast = new bootstrap.Toast(toast);
                bsToast.show();
                
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 3000);
            });
        }

        function refreshStatus() {
            // Show loading state
            const refreshBtn = document.querySelector('button[onclick="refreshStatus()"]');
            const originalText = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1 spinner-border spinner-border-sm"></i>Refreshing...';
            refreshBtn.disabled = true;

            // Make AJAX call to get updated status
            fetch(`/Invoice/GetStatus/@Model.Id`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload(); // Reload page to show updated status
                    } else {
                        alert('Error refreshing status: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error refreshing status');
                })
                .finally(() => {
                    refreshBtn.innerHTML = originalText;
                    refreshBtn.disabled = false;
                });
        }

        // Submit button handler
        document.addEventListener('DOMContentLoaded', function() {
            const submitBtn = document.getElementById('submitBtn');
            if (submitBtn) {
                submitBtn.addEventListener('click', function() {
                    if (confirm('Are you sure you want to submit this invoice to ZATCA?')) {
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.action = '/Invoice/Submit';
                        
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'id';
                        input.value = '@Model.Id';
                        
                        form.appendChild(input);
                        document.body.appendChild(form);
                        form.submit();
                    }
                });
            }
        });
    </script>
}
