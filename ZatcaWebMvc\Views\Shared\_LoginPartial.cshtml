@using Microsoft.AspNetCore.Identity
@using ZatcaWebMvc.Models

@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

<ul class="navbar-nav">
@if (SignInManager.IsSignedIn(User))
{
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            <div class="user-avatar me-2">
                <i class="bi bi-person-circle fs-5"></i>
            </div>
            <span class="d-none d-md-inline">@User.Identity?.Name</span>
        </a>
        <ul class="dropdown-menu dropdown-menu-end">
            <li>
                <h6 class="dropdown-header">
                    <i class="bi bi-person me-2"></i>@User.Identity?.Name
                </h6>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Index">
                    <i class="bi bi-gear me-2"></i>Profile Settings
                </a>
            </li>
            <li>
                <a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/ChangePassword">
                    <i class="bi bi-shield-lock me-2"></i>Change Password
                </a>
            </li>
            <li>
                <a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/PersonalData">
                    <i class="bi bi-file-person me-2"></i>Personal Data
                </a>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <form class="form-inline" asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
                    <button type="submit" class="dropdown-item text-danger">
                        <i class="bi bi-box-arrow-right me-2"></i>Logout
                    </button>
                </form>
            </li>
        </ul>
    </li>
}
else
{
    <li class="nav-item">
        <a class="nav-link" asp-area="Identity" asp-page="/Account/Register">
            <i class="bi bi-person-plus me-1"></i>Register
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" asp-area="Identity" asp-page="/Account/Login">
            <i class="bi bi-box-arrow-in-right me-1"></i>Login
        </a>
    </li>
}
</ul>

<style>
    .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark, #0056b3) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }
    
    .dropdown-menu {
        min-width: 250px;
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-radius: 0.5rem;
    }
    
    .dropdown-header {
        color: var(--bs-primary);
        font-weight: 600;
        padding: 0.75rem 1rem 0.5rem;
    }
    
    .dropdown-item {
        padding: 0.75rem 1rem;
        transition: all 0.2s ease;
    }
    
    .dropdown-item:hover {
        background-color: var(--bs-light);
        transform: translateX(2px);
    }
    
    .dropdown-item.text-danger:hover {
        background-color: rgba(var(--bs-danger-rgb), 0.1);
        color: var(--bs-danger) !important;
    }
    
    .dropdown-item i {
        width: 16px;
        text-align: center;
    }
</style>
