@using Microsoft.AspNetCore.Identity
@using ZatcaWebMvc.Models

@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

<div class="modern-flex items-center gap-2">
@if (SignInManager.IsSignedIn(User))
{
    <div class="nav-item dropdown">
        <a class="modern-navbar-link dropdown-toggle d-flex align-items-center gap-2" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            <div class="modern-user-avatar">
                <i class="bi bi-person-circle"></i>
            </div>
            <span class="d-none d-md-inline font-medium">@User.Identity?.Name</span>
        </a>
        <ul class="dropdown-menu dropdown-menu-end modern-dropdown">
            <li>
                <h6 class="modern-dropdown-header">
                    <i class="bi bi-person me-2"></i>@User.Identity?.Name
                </h6>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <a class="modern-dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Index">
                    <i class="bi bi-gear me-2"></i>Profile Settings
                </a>
            </li>
            <li>
                <a class="modern-dropdown-item" asp-area="Identity" asp-page="/Account/Manage/ChangePassword">
                    <i class="bi bi-shield-lock me-2"></i>Change Password
                </a>
            </li>
            <li>
                <a class="modern-dropdown-item" asp-area="Identity" asp-page="/Account/Manage/PersonalData">
                    <i class="bi bi-file-person me-2"></i>Personal Data
                </a>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <form class="form-inline" asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
                    <button type="submit" class="modern-dropdown-item modern-dropdown-item-danger">
                        <i class="bi bi-box-arrow-right me-2"></i>Logout
                    </button>
                </form>
            </li>
        </ul>
    </div>
}
else
{
    <a class="modern-btn modern-btn-secondary modern-btn-sm" asp-area="Identity" asp-page="/Account/Register">
        <i class="bi bi-person-plus"></i>
        <span class="d-none d-md-inline">Register</span>
    </a>
    <a class="modern-btn modern-btn-primary modern-btn-sm" asp-area="Identity" asp-page="/Account/Login">
        <i class="bi bi-box-arrow-in-right"></i>
        <span class="d-none d-md-inline">Login</span>
    </a>
}
</div>

<style>
    .modern-user-avatar {
        width: 32px;
        height: 32px;
        border-radius: var(--radius-full);
        background: var(--color-primary-600);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--color-white);
        font-size: 18px;
    }

    .modern-dropdown {
        min-width: 280px;
        border: none;
        box-shadow: var(--shadow-xl);
        border-radius: var(--radius-xl);
        border: 1px solid var(--color-gray-200);
        padding: var(--space-2) 0;
    }

    .modern-dropdown-header {
        color: var(--color-primary-600);
        font-weight: var(--font-semibold);
        padding: var(--space-3) var(--space-4) var(--space-2);
        font-size: var(--text-sm);
        margin: 0;
    }

    .modern-dropdown-item {
        padding: var(--space-3) var(--space-4);
        transition: all var(--transition-fast);
        color: var(--color-gray-700);
        text-decoration: none;
        display: flex;
        align-items: center;
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        border: none;
        background: none;
        width: 100%;
        text-align: left;
    }

    .modern-dropdown-item:hover {
        background-color: var(--color-gray-50);
        color: var(--color-primary-600);
        transform: translateX(4px);
    }

    .modern-dropdown-item-danger {
        color: var(--color-error);
    }

    .modern-dropdown-item-danger:hover {
        background-color: var(--color-error-light);
        color: var(--color-error) !important;
    }

    .modern-dropdown-item i {
        width: 16px;
        text-align: center;
        flex-shrink: 0;
    }
</style>
