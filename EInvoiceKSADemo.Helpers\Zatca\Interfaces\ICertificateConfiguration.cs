﻿/*
 * Author  : <PERSON>
 * Email   : <EMAIL>
 * LinkedIn: https://www.linkedin.com/in/ahmoosa/
 * Date    : 26/9/2022
 */
using EInvoiceKSADemo.Helpers.Zatca.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EInvoiceKSADemo.Helpers.Zatca
{
    public interface ICertificateConfiguration
    {
        CertificateDetails GetCertificateDetails();

        CertificateDetails GetCertificateDetails(string companyId);

        Task SaveCsrAndPK(InputCsrModel model);

        Task UpdateCertificate(CSIDResultModel model);

    }
}
