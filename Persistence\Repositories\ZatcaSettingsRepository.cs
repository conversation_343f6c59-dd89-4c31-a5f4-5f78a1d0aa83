﻿using Application.Contracts.IRepository;
using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Persistence.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Persistence.Repositories
{
    public class ZatcaSettingsRepository : IZatcaSettingsRepository
    {
        private readonly ApplicationDbContext _context;

        public ZatcaSettingsRepository(ApplicationDbContext context)
        {
            _context = context;
        }
        /// <summary>
        /// Getting the invoice types
        /// 1000=>	Standard Only
        /// 0100=>	Simplified Only
        /// 1100=>	Standard and Simplified
        /// </summary>
        /// <returns>Returns Lst of invoice Type that can used to make clearance of zatca </returns>
        public async Task<IReadOnlyList<InvoiceType>> GetAllInvoiceTypes()
        {
            return await _context.EI_InvoiceTypes.ToListAsync();
        }
        /// <summary>
        /// Getting the identity with different ways
        /// CRN=>	Commercial Registration Number
        /// MOM=>	Momra License
        /// MLS=>	MLSD License
        /// 700=>	700 Number
        /// SAG=>	Sagia License
        /// OTH=>	Other ID
        /// </summary>
        /// <returns>Returns the list of all identites of seller that can verify with</returns>
        public async Task<IReadOnlyList<SellerIdentity>> GetAllSellerIdentities()
        {
            return await _context.EI_SellerIdentities.ToListAsync(); 
        }
    }
}
