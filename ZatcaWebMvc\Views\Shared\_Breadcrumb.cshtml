@model List<(string Text, string? Url, bool IsActive)>

@{
    var breadcrumbs = Model ?? new List<(string, string?, bool)>();
    var showHome = ViewData["ShowHome"]?.ToString() != "false";
    var homeText = ViewData["HomeText"]?.ToString() ?? "Home";
    var homeUrl = ViewData["HomeUrl"]?.ToString() ?? "/";
}

<nav aria-label="breadcrumb">
    <ol class="breadcrumb bg-light rounded-3 px-3 py-2 mb-0">
        @if (showHome)
        {
            <li class="breadcrumb-item">
                <a href="@homeUrl" class="text-decoration-none">
                    <i class="bi bi-house me-1"></i>@homeText
                </a>
            </li>
        }
        
        @foreach (var (text, url, isActive) in breadcrumbs)
        {
            if (isActive)
            {
                <li class="breadcrumb-item active" aria-current="page">@text</li>
            }
            else
            {
                <li class="breadcrumb-item">
                    @if (!string.IsNullOrEmpty(url))
                    {
                        <a href="@url" class="text-decoration-none">@text</a>
                    }
                    else
                    {
                        @text
                    }
                </li>
            }
        }
    </ol>
</nav>

<style>
    .breadcrumb {
        font-size: 0.875rem;
        margin-bottom: 0;
        background-color: #f8f9fa !important;
        border: 1px solid #e9ecef;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: #6c757d;
        font-weight: 600;
    }
    
    .breadcrumb-item a {
        color: #495057;
        transition: color 0.2s ease;
    }
    
    .breadcrumb-item a:hover {
        color: #007bff;
    }
    
    .breadcrumb-item.active {
        color: #6c757d;
        font-weight: 500;
    }
    
    .breadcrumb .bi {
        font-size: 0.875rem;
    }
</style>
