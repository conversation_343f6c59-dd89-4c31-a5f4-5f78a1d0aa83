using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZatcaWebMvc.Models;
using ZatcaWebMvc.Services;

namespace ZatcaWebMvc.Controllers
{
    /// <summary>
    /// Controller for managing ZATCA certificates
    /// </summary>
    [Authorize]
    public class CertificateController : Controller
    {
        private readonly IZatcaApiService _zatcaApiService;
        private readonly ILogger<CertificateController> _logger;

        public CertificateController(IZatcaApiService zatcaApiService, ILogger<CertificateController> logger)
        {
            _zatcaApiService = zatcaApiService;
            _logger = logger;
        }

        /// <summary>
        /// Display list of all certificates
        /// </summary>
        public async Task<IActionResult> Index()
        {
            try
            {
                var response = await _zatcaApiService.GetAllCertificatesAsync();
                
                if (response.Success && response.Data != null)
                {
                    return View(response.Data);
                }
                
                ViewBag.ErrorMessage = response.Message;
                return View(new List<CertificateCreationResponse>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading certificates");
                ViewBag.ErrorMessage = "An error occurred while loading certificates.";
                return View(new List<CertificateCreationResponse>());
            }
        }

        /// <summary>
        /// Display certificate details
        /// </summary>
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var response = await _zatcaApiService.GetCertificateAsync(id);
                
                if (response.Success && response.Data != null)
                {
                    return View(response.Data);
                }
                
                TempData["ErrorMessage"] = response.Message;
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading certificate {CertificateId}", id);
                TempData["ErrorMessage"] = "An error occurred while loading the certificate.";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Display create certificate form
        /// </summary>
        public IActionResult Create()
        {
            var model = new CertificateCreateRequest
            {
                CountryName = "SA",
                IsProduction = false
            };
            return View(model);
        }

        /// <summary>
        /// Handle certificate creation
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CertificateCreateRequest model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var response = await _zatcaApiService.CreateCertificateAsync(model);
                
                if (response.Success && response.Data != null)
                {
                    TempData["SuccessMessage"] = "Certificate created successfully!";
                    return RedirectToAction(nameof(Details), new { id = response.Data.RequestId });
                }
                
                // Add API errors to ModelState
                foreach (var error in response.Errors)
                {
                    ModelState.AddModelError("", error);
                }
                
                ViewBag.ErrorMessage = response.Message;
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating certificate");
                ModelState.AddModelError("", "An error occurred while creating the certificate.");
                return View(model);
            }
        }

        /// <summary>
        /// Renew certificate
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Renew(int id)
        {
            try
            {
                var response = await _zatcaApiService.RenewCertificateAsync(id);

                if (response.Success)
                {
                    TempData["SuccessMessage"] = "Certificate renewal initiated successfully.";
                }
                else
                {
                    TempData["ErrorMessage"] = response.Message;
                }

                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error renewing certificate {CertificateId}", id);
                TempData["ErrorMessage"] = "An error occurred while renewing the certificate.";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        /// <summary>
        /// Get certificate status via AJAX
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetStatus(int id)
        {
            try
            {
                var response = await _zatcaApiService.GetCertificateAsync(id);

                if (response.Success && response.Data != null)
                {
                    return Json(new {
                        success = true,
                        status = response.Data.Status,
                        expiryDate = response.Data.ExpiryDate.ToString("dd/MM/yyyy"),
                        requestId = response.Data.RequestId
                    });
                }

                return Json(new { success = false, message = response.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting certificate status {CertificateId}", id);
                return Json(new { success = false, message = "An error occurred while getting certificate status." });
            }
        }
    }
}
