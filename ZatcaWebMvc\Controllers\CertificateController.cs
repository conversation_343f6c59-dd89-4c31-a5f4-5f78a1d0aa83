using Microsoft.AspNetCore.Mvc;
using ZatcaWebMvc.Models;
using ZatcaWebMvc.Services;

namespace ZatcaWebMvc.Controllers
{
    /// <summary>
    /// Controller for managing certificates
    /// </summary>
    public class CertificateController : Controller
    {
        private readonly IZatcaApiService _zatcaApiService;
        private readonly ILogger<CertificateController> _logger;

        public CertificateController(IZatcaApiService zatcaApiService, ILogger<CertificateController> logger)
        {
            _zatcaApiService = zatcaApiService;
            _logger = logger;
        }

        /// <summary>
        /// Display list of all certificates
        /// </summary>
        public async Task<IActionResult> Index()
        {
            try
            {
                var response = await _zatcaApiService.GetAllCertificatesAsync();
                
                if (response.Success && response.Data != null)
                {
                    return View(response.Data);
                }
                
                ViewBag.ErrorMessage = response.Message;
                return View(new List<CertificateCreationResponse>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading certificates");
                ViewBag.ErrorMessage = "An error occurred while loading certificates.";
                return View(new List<CertificateCreationResponse>());
            }
        }

        /// <summary>
        /// Display certificate details
        /// </summary>
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var response = await _zatcaApiService.GetCertificateAsync(id);
                
                if (response.Success && response.Data != null)
                {
                    return View(response.Data);
                }
                
                ViewBag.ErrorMessage = response.Message;
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading certificate {CertificateId}", id);
                ViewBag.ErrorMessage = "An error occurred while loading the certificate.";
                return NotFound();
            }
        }

        /// <summary>
        /// Display create certificate form
        /// </summary>
        public IActionResult Create()
        {
            var model = new CertificateCreateRequest
            {
                CountryName = "SA",
                IsProduction = false
            };
            return View(model);
        }

        /// <summary>
        /// Handle certificate creation
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CertificateCreateRequest model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var response = await _zatcaApiService.CreateCertificateAsync(model);
                
                if (response.Success)
                {
                    TempData["SuccessMessage"] = "Certificate created successfully!";
                    return RedirectToAction(nameof(Index));
                }
                
                // Add API errors to ModelState
                foreach (var error in response.Errors)
                {
                    ModelState.AddModelError("", error);
                }
                
                ViewBag.ErrorMessage = response.Message;
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating certificate");
                ModelState.AddModelError("", "An error occurred while creating the certificate.");
                return View(model);
            }
        }
    }
}
