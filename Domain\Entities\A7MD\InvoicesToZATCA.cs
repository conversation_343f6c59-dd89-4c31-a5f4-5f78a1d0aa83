﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.A7MD
{
    public partial class InvoicesToZATCA
    {
        public long DetailId { get; set; }

        public string? CompanyJSON { get; set; }

        public string? Currency { get; set; }

        public long? InvoiceId { get; set; }

        public DateTime? InvoiceCreationDate { get; set; }

        public bool? IsSalesInvoice { get; set; }

        public bool? IsRefundInvoice { get; set; }

        public bool? IsTaxInvoice { get; set; }

        public bool? IsSimplifiedInvoice { get; set; }

        public string? CustomerJson { get; set; }

        public DateTime? InvoiceDeliveryDate { get; set; }

        public string? RefundReason { get; set; }

        public decimal? TotalDiscount { get; set; }

        public decimal? TaxAmount { get; set; }

        public decimal? TaxPercentage { get; set; }

        public decimal? TotalAmount { get; set; }

        public decimal? TotalForItems { get; set; }

        public decimal? NetWithoutVAT { get; set; }

        public string? InvoiceItemsJson { get; set; }

        public int? InsertFlag { get; set; }

        public int? UpdateFlag { get; set; }

        public int? DeleteFlag { get; set; }

        public bool? IsDeleted { get; set; }

        public int? CreatorId { get; set; }

        public int? ModifierId { get; set; }

        public DateTime? CreationDate { get; set; }

        public DateTime? ModificationDate { get; set; }

        public bool? IsSent { get; set; }

        public string? InvoiceRefId { get; set; }

        public string? InvoicePayments { get; set; }

        public string PaymentMeans { get; set; }
        public string? CertificateDetailsId { get; set; }
    }
}
