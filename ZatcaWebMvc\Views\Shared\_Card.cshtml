@{
    var title = ViewData["Title"]?.ToString();
    var subtitle = ViewData["Subtitle"]?.ToString();
    var icon = ViewData["Icon"]?.ToString();
    var headerClass = ViewData["HeaderClass"]?.ToString() ?? "bg-transparent";
    var cardClass = ViewData["CardClass"]?.ToString() ?? "border-0 shadow-sm";
    var bodyClass = ViewData["BodyClass"]?.ToString();
    var showHeader = !string.IsNullOrEmpty(title) || !string.IsNullOrEmpty(subtitle) || !string.IsNullOrEmpty(icon);
    var headerActions = ViewData["HeaderActions"]?.ToString();
}

<div class="card @cardClass">
    @if (showHeader)
    {
        <div class="card-header @headerClass border-0">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    @if (!string.IsNullOrEmpty(icon))
                    {
                        <i class="@icon me-2 text-primary"></i>
                    }
                    <div>
                        @if (!string.IsNullOrEmpty(title))
                        {
                            <h5 class="card-title mb-0">@title</h5>
                        }
                        @if (!string.IsNullOrEmpty(subtitle))
                        {
                            <p class="card-subtitle text-muted mb-0 small">@subtitle</p>
                        }
                    </div>
                </div>
                @if (!string.IsNullOrEmpty(headerActions))
                {
                    <div class="card-header-actions">
                        @Html.Raw(headerActions)
                    </div>
                }
            </div>
        </div>
    }
    <div class="card-body @bodyClass">
        <!-- Card content will be provided by the calling view -->
    </div>
</div>

<style>
    .card {
        transition: all 0.2s ease-in-out;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }
    
    .card-title {
        font-weight: 600;
        color: #2c3e50;
    }
    
    .card-subtitle {
        font-size: 0.875rem;
    }
    
    .card-header-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
</style>
