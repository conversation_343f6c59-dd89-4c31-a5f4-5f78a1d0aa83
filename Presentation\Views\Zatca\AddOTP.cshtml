﻿
@{
    ViewData["Title"] = "AddOTP";
    @model OTP;
}

<h1>AddOTP</h1>
<form asp-action="AddOTP"
      data-ajax-method="post"
      data-ajax="true"
      data-ajax-success="ShowSuccessMessage"
      data-ajax-failure="ShowErrorMessage">

    <div class="row">
        <div class="col-12">
            <label  class="form-label">OTP </label>
            <input type="text" class="form-control" asp-for="@Model.Value">
            <span asp-validation-for="@Model.Value" class="text-danger"></span>
        </div>

        
    </div>



    <div class="col-12 mt-3 ">
        <button type="submit" class="btn btn-primary">Add OTP</button>
    </div>
</form>

@section Scripts {
    <script>
        function ShowSuccessMessage() {
            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: 'Saved successfully!',
                customClass: {
                    confirmButton: "btn btn-primary"
                }
            });
        }
        function ShowErrorMessage(message = 'Something went wrong!') {
            console.log(message)
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: message.responseText != undefined ? message.responseText : message,
                customClass: {
                    confirmButton: "btn btn-primary"
                }
            });
        }
    </script>
    <partial name="_ValidationScriptsPartial" />
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

}
