@model ZatcaWebMvc.Models.InvoiceCreateRequest
@{
    ViewData["Title"] = "Create Invoice";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-action="Index">Invoices</a></li>
                    <li class="breadcrumb-item active">Create</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="bi bi-plus-circle me-2"></i>Create New Invoice
            </h1>
            <p class="text-muted mb-0">Create a new invoice for ZATCA submission</p>
        </div>
        <div>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Back to List
            </a>
        </div>
    </div>

    <form asp-action="Create" method="post">
        <div class="row">
            <!-- Invoice Details -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-info-circle me-1"></i>Invoice Details
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="InvoiceNumber" class="form-label"></label>
                                    <input asp-for="InvoiceNumber" class="form-control" placeholder="INV-2024-001" />
                                    <span asp-validation-for="InvoiceNumber" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="CustomerName" class="form-label"></label>
                                    <input asp-for="CustomerName" class="form-control" placeholder="Customer Name" />
                                    <span asp-validation-for="CustomerName" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="InvoiceDate" class="form-label"></label>
                                    <input asp-for="InvoiceDate" class="form-control" type="date" />
                                    <span asp-validation-for="InvoiceDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="TotalAmount" class="form-label"></label>
                                    <div class="input-group">
                                        <span class="input-group-text">SAR</span>
                                        <input asp-for="TotalAmount" class="form-control" placeholder="0.00"
                                               step="0.01" min="0" id="totalAmount" />
                                    </div>
                                    <span asp-validation-for="TotalAmount" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="VatAmount" class="form-label"></label>
                                    <div class="input-group">
                                        <span class="input-group-text">SAR</span>
                                        <input asp-for="VatAmount" class="form-control" placeholder="0.00"
                                               step="0.01" min="0" id="vatAmount" />
                                    </div>
                                    <span asp-validation-for="VatAmount" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Net Amount</label>
                                    <div class="input-group">
                                        <span class="input-group-text">SAR</span>
                                        <input type="text" class="form-control fw-bold text-success"
                                               id="netAmount" readonly placeholder="0.00" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label asp-for="Description" class="form-label"></label>
                            <textarea asp-for="Description" class="form-control" rows="3" 
                                      placeholder="Invoice description or notes"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary and Actions -->
            <div class="col-lg-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-calculator me-1"></i>Invoice Summary
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Total Amount:</span>
                            <span id="summaryTotal" class="fw-bold">SAR 0.00</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>VAT Amount:</span>
                            <span id="summaryVat" class="fw-bold">SAR 0.00</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <span class="h6">Net Amount:</span>
                            <span id="summaryNet" class="h6 text-success fw-bold">SAR 0.00</span>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-1"></i>Create Invoice
                            </button>
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Validation Summary -->
                @if (!ViewData.ModelState.IsValid)
                {
                    <div class="card shadow border-danger">
                        <div class="card-header bg-danger text-white py-3">
                            <h6 class="m-0 font-weight-bold">
                                <i class="bi bi-exclamation-triangle me-1"></i>Validation Errors
                            </h6>
                        </div>
                        <div class="card-body">
                            <div asp-validation-summary="All" class="text-danger"></div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </form>
</div>

@section Scripts {
    <script>
        function updateCalculations() {
            const totalAmount = parseFloat(document.getElementById('totalAmount').value) || 0;
            const vatAmount = parseFloat(document.getElementById('vatAmount').value) || 0;
            const netAmount = totalAmount - vatAmount;

            // Update net amount field
            document.getElementById('netAmount').value = netAmount.toFixed(2);

            // Update summary
            document.getElementById('summaryTotal').textContent = `SAR ${totalAmount.toFixed(2)}`;
            document.getElementById('summaryVat').textContent = `SAR ${vatAmount.toFixed(2)}`;
            document.getElementById('summaryNet').textContent = `SAR ${netAmount.toFixed(2)}`;
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            updateCalculations();

            document.getElementById('totalAmount').addEventListener('input', updateCalculations);
            document.getElementById('vatAmount').addEventListener('input', updateCalculations);
        });
    </script>
}
