﻿@page
@model ChangePasswordModel
@{
    ViewData["Title"] = "Change Password";
    ViewData["ActivePage"] = ManageNavPages.ChangePassword;
    Layout = "_Layout";
}

<div class="modern-container py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Change Password</h1>
            <p class="text-gray-600">Update your account password for enhanced security</p>
        </div>

        <!-- Status Message -->
        <partial name="_StatusMessage" for="StatusMessage" />

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Navigation Sidebar -->
            <div class="lg:col-span-1">
                <partial name="_ManageNav" />
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="modern-card">
                    <div class="modern-card-header">
                        <h2 class="text-xl font-semibold text-gray-900">Update Password</h2>
                        <p class="text-sm text-gray-600">Choose a strong password to keep your account secure</p>
                    </div>

                    <form id="change-password-form" method="post" class="space-y-6">
                        <div asp-validation-summary="ModelOnly" class="modern-alert modern-alert-error" role="alert" style="display: none;"></div>

                        <!-- Current Password -->
                        <div class="modern-form-group">
                            <div class="modern-floating-input-container">
                                <input asp-for="Input.OldPassword"
                                       class="modern-floating-input"
                                       type="password"
                                       autocomplete="current-password"
                                       aria-required="true"
                                       placeholder=" " />
                                <label asp-for="Input.OldPassword" class="modern-floating-label">
                                    <i class="bi bi-shield-lock me-2"></i>Current Password
                                </label>
                            </div>
                            <span asp-validation-for="Input.OldPassword" class="modern-form-error"></span>
                        </div>

                        <!-- New Password -->
                        <div class="modern-form-group">
                            <div class="modern-floating-input-container">
                                <input asp-for="Input.NewPassword"
                                       class="modern-floating-input"
                                       type="password"
                                       autocomplete="new-password"
                                       aria-required="true"
                                       placeholder=" " />
                                <label asp-for="Input.NewPassword" class="modern-floating-label">
                                    <i class="bi bi-key me-2"></i>New Password
                                </label>
                            </div>
                            <span asp-validation-for="Input.NewPassword" class="modern-form-error"></span>
                            <div class="text-xs text-gray-500 mt-1">
                                Password must be at least 6 characters long and contain uppercase, lowercase, and numbers.
                            </div>
                        </div>

                        <!-- Confirm New Password -->
                        <div class="modern-form-group">
                            <div class="modern-floating-input-container">
                                <input asp-for="Input.ConfirmPassword"
                                       class="modern-floating-input"
                                       type="password"
                                       autocomplete="new-password"
                                       aria-required="true"
                                       placeholder=" " />
                                <label asp-for="Input.ConfirmPassword" class="modern-floating-label">
                                    <i class="bi bi-shield-check me-2"></i>Confirm New Password
                                </label>
                            </div>
                            <span asp-validation-for="Input.ConfirmPassword" class="modern-form-error"></span>
                        </div>

                        <!-- Security Tips -->
                        <div class="modern-alert modern-alert-info">
                            <div class="flex">
                                <i class="bi bi-info-circle text-blue-500 me-3 mt-0.5"></i>
                                <div>
                                    <h4 class="font-medium text-blue-900 mb-1">Password Security Tips</h4>
                                    <ul class="text-sm text-blue-800 space-y-1">
                                        <li>• Use a combination of uppercase and lowercase letters</li>
                                        <li>• Include numbers and special characters</li>
                                        <li>• Avoid using personal information or common words</li>
                                        <li>• Consider using a password manager</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end">
                            <button type="submit" class="modern-btn modern-btn-primary">
                                <i class="bi bi-shield-check me-2"></i>
                                Update Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
