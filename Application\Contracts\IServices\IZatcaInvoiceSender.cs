﻿using Application.Models.Zatca;
using Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Contracts.IServices
{
    public interface IZatcaInvoiceSender
    {
        Task<ZatcaInvoiceResultResponse> SendInvoiceToZatcaAsync(InvoiceToZatca invoice);
        Task<ZatcaInvoiceResultResponse> SendInvoiceToZatcaAsync();
        Task<ZatcaInvoiceResultResponse> SendInvoiceToZatcaAsync(InvoiceToZatca invoice, Seller supplier,
            CertificateSettings certificateDetails);
        Task<ZatcaInvoiceResultResponse> SendInvoiceToZatcaAsync(InvoiceToZatca invoice,CertificateSettings certificateDetails);

    }
}
