﻿using Application.Contracts.IRepository;
using Application.Contracts.Zatca;
using Application.Models.Invoice;
using Application.Models.Zatca;
using Domain.Entities;
using Domain.Enums;
using EInvoiceKSADemo.Helpers.Zatca.Constants;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace EInvoiceKSADemo.Helpers.Zatca.Helpers
{
    public class ZatcaCSIDIssuer : IZatcaCSIDIssuer
    {
        private readonly IZatcaAPICaller _zatcaAPICaller;
        private readonly ICsrReader _csrReader;
        private readonly IXmlInvoiceGenerator _xmlGenerator;
        private readonly IInvoiceSigner _signer;
        private readonly ILogger<ZatcaCSIDIssuer> _logger;
        private readonly ICertificateSettingsRepository _certificateSettingsRepository;
        private readonly ISupplierRepository _supplierRepository;
        private readonly IInvoiceRepository _invoiceRepository;
        public ZatcaCSIDIssuer(IZatcaAPICaller zatcaAPICaller, ICsrReader csrReader
            , IXmlInvoiceGenerator xmlGenerator, IInvoiceSigner signer,
            ILogger<ZatcaCSIDIssuer> logger
            ,ICertificateSettingsRepository certificateSettingsRepository, ISupplierRepository supplierRepository, IInvoiceRepository invoiceRepository)
        {
            this._zatcaAPICaller = zatcaAPICaller;
            this._csrReader = csrReader;
            this._xmlGenerator = xmlGenerator;
            this._signer = signer;
            _logger = logger;
            _certificateSettingsRepository = certificateSettingsRepository;
            _supplierRepository = supplierRepository;
            _invoiceRepository = invoiceRepository;
        }

        public Supplier Supplier { get; set; }
        public async Task<CSIDResultModel> OnboardingCSIDAsync(InputCSIDOnboardingModel model)
        {
            if (string.IsNullOrEmpty(model.CSR))
            {
                return null;
            }
            var companyCsr = model.CSR;
            this.Supplier = model.Supplier;
            if (!string.IsNullOrEmpty(companyCsr))
            {
                _logger.LogInformation("CompleteComplianceCSIDAsync is going to start");

                var complianceResult = await _zatcaAPICaller.CompleteComplianceCSIDAsync(new InputComplianceModel
                {
                    CSR = companyCsr
                }, model.OTP);

                _logger.LogInformation($"CompleteComplianceCSIDAsync has been " +
                    $"finished and BinarySecurityToken is {complianceResult?.BinarySecurityToken} " +
                    $"and errors are {complianceResult?.Errors}");

                if (!string.IsNullOrEmpty(complianceResult?.BinarySecurityToken))
                {
                    SharedData.UserName = complianceResult.BinarySecurityToken;
                    SharedData.Secret = complianceResult.Secret;

                    var csrResult = _csrReader.GetCsrInvoiceType(companyCsr);
                    if (csrResult != null)
                    {
                        if (csrResult.StandardAllowed)
                        {
                            var invoiceDataModel = await GetInvoiceModel(Domain.Enums.InvoiceType.Standard, InvoiceTypeCode.Invoice, TransactionTypeCode.Standard);
                            var invoiceDataModel1 = new InvoiceDataModel();
                            invoiceDataModel1 = invoiceDataModel; // Assuming there's a Clone() method to create a copy of invoiceDataModel
                            //invoiceDataModel.TotalWithoutTax
                            // Edit values as per the provided code
                            invoiceDataModel1.Supplier.SellerName = model.Supplier.SellerName;
                            invoiceDataModel1.Supplier.SellerTRN = model.Supplier.SellerTRN;
                            invoiceDataModel1.Supplier.AdditionalStreetAddress = model.Supplier.AdditionalStreetAddress;
                            invoiceDataModel1.Supplier.BuildingNumber = model.Supplier.BuildingNumber;
                            invoiceDataModel1.Supplier.CityName = model.Supplier.CityName;
                            invoiceDataModel1.Supplier.IdentityNumber = model.Supplier.IdentityNumber;
                            invoiceDataModel1.Supplier.IdentityType = model.Supplier.IdentityType;
                            invoiceDataModel1.Supplier.CountryCode = model.Supplier.CountryCode;
                            invoiceDataModel1.Supplier.DistrictName = model.Supplier.DistrictName;
                            invoiceDataModel1.Supplier.PostalCode = model.Supplier.PostalCode;
                            invoiceDataModel1.Supplier.StreetName = model.Supplier.StreetName;

                            invoiceDataModel1.IssueDate = DateTime.Now.ToString("yyyy-MM-dd");
                            invoiceDataModel1.IssueTime = DateTime.Now.ToString("HH:mm:ssZ");
                            invoiceDataModel1.PreviousInvoiceHash =  await GetPreviousInvoiceHashAsync();
                            invoiceDataModel1.DeliveryDate = DateTime.Now.ToString("yyyy-MM-dd");

                            invoiceDataModel1.TransactionTypeCode = TransactionTypeCode.Standard;

                            // Add Customer details
                            var customer = new Customer
                            {
                                CustomerName = "Ahmed Ali",
                                IdentityNumber = "311111111111113",
                                IdentityType = "NAT",
                                VatRegNumber = "323042342342333",
                                StreetName = "Makka",
                                BuildingNumber = "1111",
                                ZipCode = "12345",
                                CityName = "Al Riyadh",
                                DistrictName = "CustomerAddressDistrict",
                                RegionName = "Al Riyadh"
                            };

                            invoiceDataModel1.Customer = customer;

                            // Set TransactionTypeCode and InvoiceTypeCode for invoiceDataModel1
                            invoiceDataModel1.TransactionTypeCode = TransactionTypeCode.Standard;
                            invoiceDataModel1.InvoiceTypeCode = (int)InvoiceTypeCode.Invoice;

                            var invoiceSigned = GetSignedXmlResult(invoiceDataModel1);
                            invoiceDataModel1.InvoiceTypeCode = (int)InvoiceTypeCode.Debit;
                            invoiceDataModel1.Notes = "Cancellation or suspension of the supplies after its occurrence either wholly or partially";
                            invoiceDataModel1.ReferenceId = "INV2010";


                            var debitSigned = GetSignedXmlResult(invoiceDataModel1);
                            invoiceDataModel1.InvoiceTypeCode = (int)InvoiceTypeCode.Credit;

                            var creditSigned = GetSignedXmlResult(invoiceDataModel1);

                            foreach (var m in new[] { invoiceSigned, debitSigned, creditSigned })
                            {
                                if (m != null)
                                {
                                    var invoiceCompliance = await _zatcaAPICaller.PerformComplianceCheckAsync(new InputInvoiceModel
                                    {
                                        Invoice = m.InvoiceAsBase64,
                                        InvoiceHash = m.InvoiceHash,
                                        UUID = m.UUID
                                    });
                                    if (invoiceCompliance?.ClearanceStatus != "CLEARED")
                                    {
                                        return null;
                                    }
                                }
                                else
                                {
                                    return null;
                                }
                            }
                        }

                        if (csrResult.SimplifiedAllowed)
                        {
                            var invoiceModel = await GetInvoiceModel(Domain.Enums.InvoiceType.Simplified, InvoiceTypeCode.Invoice, TransactionTypeCode.Simplified);
                            var invoiceSigned = GetSignedXmlResult(invoiceModel);
                            var debitModel = await GetInvoiceModel(Domain.Enums.InvoiceType.Simplified, InvoiceTypeCode.Debit, TransactionTypeCode.Simplified);
                            var debitSigned = GetSignedXmlResult(debitModel);
                            var creditModel = await GetInvoiceModel(Domain.Enums.InvoiceType.Simplified, InvoiceTypeCode.Credit, TransactionTypeCode.Simplified);
                            var creditSigned = GetSignedXmlResult(creditModel);

                            foreach (var m in new[] { invoiceSigned, debitSigned, creditSigned })
                            {
                                if (m != null)
                                {
                                    var invoiceCompliance = await _zatcaAPICaller.PerformComplianceCheckAsync(new InputInvoiceModel
                                    {
                                        Invoice = m.InvoiceAsBase64,
                                        InvoiceHash = m.InvoiceHash,
                                        UUID = m.UUID
                                    });
                                    if (invoiceCompliance?.ReportingStatus != "REPORTED")
                                    {
                                        return null;
                                    }
                                }
                                else
                                {
                                    return null;
                                }
                            }
                        }
                        

                        _logger.LogInformation("OnboardingCSIDAsync is going to start");

                        var certResult = await _zatcaAPICaller.OnboardingCSIDAsync(new InputCSIDModel
                        {
                            compliance_request_id = complianceResult.RequestId.ToString()
                        });

                        if (certResult?.RequestId > 0)
                        {
                            X509Certificate2 certificate = new X509Certificate2(Convert.FromBase64String(certResult.BinarySecurityToken));
                            if (certificate != null)
                            {
                                return new CSIDResultModel
                                {
                                    Secret = certResult.Secret,
                                    Certificate = certResult.BinarySecurityToken,
                                    StartedDate = certificate.NotBefore,
                                    ExpiredDate = certificate.NotAfter,
                                };
                            }
                        }
                    }
                }
            }
            return null;
        }
        public async Task SendInvoiceAsync()
        {
            Domain.Entities.CertificateSettings CSR = _certificateSettingsRepository.GetCertificateSettings();

            if (!string.IsNullOrEmpty(CSR?.UserName))
            {
                SharedData.UserName = CSR?.UserName;
                SharedData.Secret = CSR?.Secret;

                    //if (csrResult.StandardAllowed)
                    //{
                        var invoiceModel = await GetInvoiceModel(Domain.Enums.InvoiceType.Standard, InvoiceTypeCode.Invoice, TransactionTypeCode.Standard);
                        var invoiceSigned = GetSignedXmlResult(invoiceModel);
                        var debitModel = await GetInvoiceModel(Domain.Enums.InvoiceType.Standard, InvoiceTypeCode.Debit, TransactionTypeCode.Standard);
                        var debitSigned = GetSignedXmlResult(debitModel);
                        var creditModel = await GetInvoiceModel(Domain.Enums.InvoiceType.Standard, InvoiceTypeCode.Credit, TransactionTypeCode.Standard);
                        var creditSigned = GetSignedXmlResult(creditModel);

                        foreach (var m in new[] { invoiceSigned, debitSigned, creditSigned })
                        {
                            if (m != null)
                            {
                                var invoiceCompliance = await _zatcaAPICaller.ReportSingleInvoiceAsync(new InputInvoiceModel
                                {
                                    Invoice = m.InvoiceAsBase64,
                                    InvoiceHash = m.InvoiceHash,
                                    UUID = m.UUID
                                }, 0);
                                //if (invoiceCompliance?.ClearanceStatus != "CLEARED")
                                //{
                                //    return null;
                                //}
                            }
                            //else
                            //{
                            //    return null;
                            //}
                        }
                    }

                    //if (csrResult.SimplifiedAllowed)
                    //{
                    //    var invoiceModel =await GetInvoiceModel(InvoiceType.Simplified, InvoiceTypeCode.Invoice, TransactionTypeCode.Simplified);
                    //    var invoiceSigned = GetSignedXmlResult(invoiceModel);
                    //    var debitModel =await GetInvoiceModel(InvoiceType.Simplified, InvoiceTypeCode.Debit, TransactionTypeCode.Simplified);
                    //    var debitSigned = GetSignedXmlResult(debitModel);
                    //    var creditModel = await GetInvoiceModel(InvoiceType.Simplified, InvoiceTypeCode.Credit, TransactionTypeCode.Simplified);
                    //    var creditSigned = GetSignedXmlResult(creditModel);

                    //    foreach (var m in new[] { invoiceSigned, debitSigned, creditSigned })
                    //    {
                    //        if (m != null)
                    //        {
                    //            var invoiceCompliance = await _zatcaAPICaller.ClearSingleInvoiceAsync(new InputInvoiceModel
                    //            {
                    //                Invoice = m.InvoiceAsBase64,
                    //                InvoiceHash = m.InvoiceHash,
                    //                UUID = m.UUID
                    //            }, 1);
                    //            //if (invoiceCompliance?.ReportingStatus != "REPORTED")
                    //            //{
                    //            //    return null;
                    //            //}
                    //        }
                    //        //else
                    //        //{
                    //        //    return null;
                    //        //}
                    //    }
                    //}



                //}
            //}
        }

        public async Task<InvoiceDataModel> GetInvoiceModel(Domain.Enums.InvoiceType invoiceType, InvoiceTypeCode invoiceTypeCode, string transactionTypeCode)
        {

            var a = await _supplierRepository.GetSupplierAsync();
            var model = new InvoiceDataModel
            {
                InvoiceNumber = GetNextInvoiceNumber(invoiceTypeCode,112),
                InvoiceType = (int)invoiceType,
                InvoiceTypeCode = (int)invoiceTypeCode,
                Id = Guid.NewGuid().ToString(),
                Order = 2,
                TransactionTypeCode = transactionTypeCode,
                Lines = new List<LineItem>
                {
                    new LineItem {  Index = 1, ProductName = "T-Shirt1" , Quantity = 1, NetPrice = 40 , Tax = 15.00},
                    new LineItem {  Index = 2, ProductName = "LCD Screen10" , Quantity = 4, NetPrice = 5000 , Tax = 15.00},
                },
                //Tax = 15.00,
                //PaymentMeansCode = 10,
                PaymentMeans = new List<PaymentMean> { new PaymentMean { PaymentMeansCode = 10 } },
                Supplier = new Supplier
                {
                    SellerName = a.SellerName,
                    SellerTRN = a.SellerTRN,
                    StreetName = a.StreetName,
                    AdditionalStreetAddress = a.AdditionalStreetAddress,
                    BuildingNumber = a.BuildingNumber,
                    CityName = a.CityName,
                    CountryCode = a.CountryCode,
                    DistrictName = a.DistrictName,
                    IdentityNumber = a.IdentityNumber,
                    IdentityType = a.IdentityType,
                    PostalCode = a.PostalCode,
                },
                Customer = new Customer
                {
                    CustomerName = "Saleh Saleh",
                    IdentityNumber = "311111111111113",
                    IdentityType = "NAT",
                    VatRegNumber = "323042342342333",
                    StreetName = "Makka",
                    BuildingNumber = "1111",
                    ZipCode = "12345",
                    CityName = "Al Riyadh",
                    DistrictName = "Al Olia",
                    RegionName = "Al Riyadh"
                },
                IssueDate = "2024-05-26",
                IssueTime = "17:00:00Z",
                PreviousInvoiceHash = await GetPreviousInvoiceHashAsync(),
                DeliveryDate = "2024-05-26"
               
            };

            //if (invoiceType == InvoiceType.Simplified)
            //{
            //    model.Customer = null;
            //}

            if (invoiceTypeCode == InvoiceTypeCode.Credit || invoiceTypeCode == InvoiceTypeCode.Debit)
            {
                model.Notes = "Cancellation or suspension of the supplies after its occurrence either wholly or partially";
                model.ReferenceId = "INV2010";
            }

            return model;
        }

        public async Task<InvoiceDataModel> GetInvoiceModel(InvoiceToZatca invoice)
        {
            var invoiceItems = await GetInvoiceItemsAsync(invoice.InvoiceItemsJson);

            var supplier = await _supplierRepository.GetSupplierAsync();

            var invoiceDataModel = new InvoiceDataModel
            {
                Id =Guid.NewGuid().ToString() ,//invoice.InvoiceId.ToString()
                
                //Id = invoice.InvoiceId.ToString(),//guid
                //Id = invoice.Id.ToString(),//guid
                Order = (int)invoice.DetailId,
                //Tax = (double)invoice.TaxPercentage,
                //                Discount = 30.00,
                Lines = invoiceItems,
                //PaymentMeansCode = (int)invoice.PaymentMeans,
                PaymentMeans = new List<PaymentMean> { new PaymentMean { PaymentMeansCode = 10 } },

                Supplier = new Supplier
                {
                    SellerName = supplier.SellerName,
                    SellerTRN = supplier.SellerTRN,
                    AdditionalStreetAddress = supplier.AdditionalStreetAddress,
                    BuildingNumber = supplier.BuildingNumber,
                    CityName = supplier.CityName,
                    IdentityNumber = supplier.IdentityNumber,
                    IdentityType = supplier.IdentityType,
                    CountryCode = supplier.CountryCode,
                    DistrictName = supplier.DistrictName,
                    PostalCode = supplier.PostalCode,
                    StreetName = supplier.StreetName,
                },
                //Customer = null,
                IssueDate = invoice.InvoiceCreationDate?.ToString("yyyy-MM-dd"),// "2022-09-26",
                IssueTime = invoice.InvoiceCreationDate?.ToString("HH:mm:ssZ"), // "17:00:00Z",
                DeliveryDate = invoice.InvoiceDeliveryDate?.ToString("yyyy-MM-dd"),
            };
            if (invoice.CertificateDetailsId != null || !String.IsNullOrEmpty(invoice.CertificateDetailsId))
            {
                invoiceDataModel.PreviousInvoiceHash = await GetPreviousInvoiceHashAsync(invoice.CertificateDetailsId);
            }
            else
            {
                invoiceDataModel.PreviousInvoiceHash = await GetPreviousInvoiceHashAsync();
            }

            if (!invoice.IsSimplifiedInvoice ?? false)
            {
                invoiceDataModel.TransactionTypeCode = TransactionTypeCode.Standard;
                invoiceDataModel.InvoiceType= (int)Domain.Enums.InvoiceType.Standard;
                //var customer = new Customer
                //{
                //    CustomerName = invoice.CustomerName,
                //    IdentityNumber = "311111111111113",
                //    IdentityType = "NAT",
                //    VatRegNumber = "323042342342333",
                //    StreetName = "Makka",
                //    BuildingNumber = "1111",
                //    ZipCode = "12345",
                //    CityName = invoice.CustomerAddressCity,
                //    DistrictName = invoice.CustomerAddressDistrict,
                //    RegionName = "Al Riyadh"
                //};

                invoiceDataModel.Customer = await GetInvoiceCustomerAsync(invoice.CustomerJson);
            }
            else
            {
                invoiceDataModel.TransactionTypeCode = TransactionTypeCode.Simplified;
                invoiceDataModel.InvoiceType = (int)Domain.Enums.InvoiceType.Simplified;
            }

            bool isRefund = invoice.IsRefundInvoice ?? false;
            bool isDebit = invoice.IsDebitInvoice ?? false;
            if (!isRefund)
            {
                invoiceDataModel.InvoiceTypeCode = (int)InvoiceTypeCode.Invoice;
            }
            else if (isRefund&& isDebit)
            {
                invoiceDataModel.InvoiceTypeCode = (int)InvoiceTypeCode.Debit;
                invoiceDataModel.Notes = invoice.RefundReason;
                invoiceDataModel.ReferenceId = invoice.InvoiceReferenceId;
            }
            else 
            {
                invoiceDataModel.InvoiceTypeCode = (int)InvoiceTypeCode.Credit;
                invoiceDataModel.Notes = invoice.RefundReason;
                invoiceDataModel.ReferenceId = invoice.InvoiceReferenceId;
            }
            invoiceDataModel.InvoiceNumber = GetNextInvoiceNumber((InvoiceTypeCode)invoiceDataModel.InvoiceTypeCode, invoice.InvoiceId ?? 0);
            if (!String.IsNullOrEmpty(invoice.ChargesJson))
            {
                invoiceDataModel.Charges = await GetInvoiceChargesAsync(invoice.ChargesJson);
            }
            //var model = new InvoiceDataModel
            //{

            //    InvoiceNumber = GetNextInvoiceNumber(invoiceTypeCode,invoice.InvoiceId??0),
            //    InvoiceType = (int)invoiceType,
            //    InvoiceTypeCode = (int)invoiceTypeCode,
            //    Id = Guid.NewGuid().ToString(),
            //    Order = 1,
            //    TransactionTypeCode = transactionTypeCode,
            //    Lines = new List<LineItem>
            //    {
            //        new LineItem {  Index = 1, ProductName = "T-Shirt1" , Quantity = 1, NetPrice = 40 , Tax = 15.00},
            //        new LineItem {  Index = 2, ProductName = "LCD Screen10" , Quantity = 4, NetPrice = 5000 , Tax = 15.00},
            //    },
            //    //Tax = 15.00,
            //    //PaymentMeansCode = 10,
            //    PaymentMeans = new List<PaymentMean> { new PaymentMean { PaymentMeansCode = 10 } },
            //    Supplier = new Supplier
            //    {
            //        SellerName = a.SellerName,
            //        SellerTRN = a.SellerTRN,
            //        StreetName = a.StreetName,
            //        AdditionalStreetAddress = a.AdditionalStreetAddress,
            //        BuildingNumber = a.BuildingNumber,
            //        CityName = a.CityName,
            //        CountryCode = a.CountryCode,
            //        DistrictName = a.DistrictName,
            //        IdentityNumber = a.IdentityNumber,
            //        IdentityType = a.IdentityType,
            //        PostalCode = a.PostalCode,
            //    },
            //    Customer = new Customer
            //    {
            //        CustomerName = "Saleh Saleh",
            //        IdentityNumber = "311111111111113",
            //        IdentityType = "NAT",
            //        VatRegNumber = "323042342342333",
            //        StreetName = "Makka",
            //        BuildingNumber = "1111",
            //        ZipCode = "12345",
            //        CityName = "Al Riyadh",
            //        DistrictName = "Al Olia",
            //        RegionName = "Al Riyadh"
            //    },
            //    IssueDate = "2024-05-26",
            //    IssueTime = "17:00:00Z",
            //    PreviousInvoiceHash = await GetPreviousInvoiceHashAsync(),
            //    DeliveryDate = "2024-05-26"

            //};

            //if (invoiceType == InvoiceType.Simplified)
            //{
            //    model.Customer = null;
            //}

            //if (invoiceTypeCode == InvoiceTypeCode.Credit || invoiceTypeCode == InvoiceTypeCode.Debit)
            //{
            //    model.Notes = "Cancellation or suspension of the supplies after its occurrence either wholly or partially";
            //    model.ReferenceId = "INV2010";
            //}

            return invoiceDataModel;
        }


        private ZatcaResult GetSignedXmlResult(InvoiceDataModel model)
        {
            // 01 - Generate XML 
            var xmlStream = ZatcaUtility.ReadInternalEmbededResourceStream(XslSettings.Embeded_InvoiceXmlFile);

            var invoiceXml = _xmlGenerator.GenerateInvoiceAsXml(xmlStream, model);

            // 02- Sign XML
            var signingResult = _signer.Sign(invoiceXml);

            // 03- Report to API
            if (signingResult.IsValid)
            {
                return signingResult;
            }

            return null;
        }

        public async Task<CSIDResultModel> RenewCSIDAsync(InputCSIDRenewingModel model)
        {
            if (string.IsNullOrEmpty(model.CSR))
            {
                return null;
            }
            if (!string.IsNullOrEmpty(model.CSR))
            {
                var renewalResult = await _zatcaAPICaller.RenewalCSIDAsync(new InputComplianceModel
                {
                    CSR = model.CSR
                }, model.OTP);


                X509Certificate2 certificate = new X509Certificate2(Convert.FromBase64String(renewalResult.BinarySecurityToken));
                if (certificate != null)
                {
                    return new CSIDResultModel
                    {
                        Secret = renewalResult.Secret,
                        Certificate = renewalResult.BinarySecurityToken,
                        StartedDate = certificate.NotBefore,
                        ExpiredDate = certificate.NotAfter,
                    };
                }
            }
            return null;
        }
        #region Helper Methods
        private async Task<string> GetPreviousInvoiceHashAsync()
        {
            string hash=await _invoiceRepository.GetPreviousHashAsync();
            return string.IsNullOrEmpty(hash)==false? hash : "NWZlY2ViNjZmZmM4NmYzOGQ5NTI3ODZjNmQ2OTZjNzljMmRiYzIzOWRkNGU5MWI0NjcyOWQ3M2EyN2ZiNTdlOQ==";
        }
        private async Task<string> GetPreviousInvoiceHashAsync(string CertificateId)
        {
            string hash=await _invoiceRepository.GetPreviousHashAsync(CertificateId);
            return string.IsNullOrEmpty(hash) == false ? hash : "NWZlY2ViNjZmZmM4NmYzOGQ5NTI3ODZjNmQ2OTZjNzljMmRiYzIzOWRkNGU5MWI0NjcyOWQ3M2EyN2ZiNTdlOQ==";
        }
        private static string RemoveWrappingQuotes(string WrappedJsonObject)
        {
            // Remove wrapping quotes if they exist
            if (WrappedJsonObject.StartsWith("\"") && WrappedJsonObject.EndsWith("\""))
            {
                WrappedJsonObject = WrappedJsonObject.Substring(1, WrappedJsonObject.Length - 2);
            }

            // Unescape quotes in the JSON string
            string unescapedJson = WrappedJsonObject.Replace("\\\"", "\"");
            return unescapedJson;
        }
        private async Task<List<Charge>> GetInvoiceChargesAsync(string invoiceChargesJson)
        {
            string unescapedJson = RemoveWrappingQuotes(invoiceChargesJson);
            List<Charge> charges = JsonSerializer.Deserialize<List<Charge>>(unescapedJson);
            return charges;
        }
        private async Task<Customer> GetInvoiceCustomerAsync(string invoiceCustomerJson)
        {
            string unescapedJson = RemoveWrappingQuotes(invoiceCustomerJson);
            Customer customer = JsonSerializer.Deserialize<Customer>(unescapedJson);
            return customer;
        }
        private async Task<List<LineItem>> GetInvoiceItemsAsync(string invoiceItemsJson)
        {
            List<InvoiceItemModel> invoiceItems = DeSerializeInvoiceItems(invoiceItemsJson);

            var lineItems = CastInvoiceItemModelListToLineItemList(invoiceItems);

            return lineItems;
        }

        private List<InvoiceItemModel> DeSerializeInvoiceItems(string invoiceItemsJson)
        {
            string unescapedJson = RemoveWrappingQuotes(invoiceItemsJson);

            var items = JsonSerializer.Deserialize<List<InvoiceItemModel>>(unescapedJson);

            return items;
        }

        private string GetNextInvoiceNumber(InvoiceTypeCode invoiceType,long InvoiceID )
        {
            switch (invoiceType)
            {
                case InvoiceTypeCode.Invoice:
                    return $"INV{InvoiceID}";
                case InvoiceTypeCode.Prepayment:
                    return $"PRP{InvoiceID}";
                case InvoiceTypeCode.Credit:
                    return $"CRD{InvoiceID}";
                 case InvoiceTypeCode.Debit:
                    return $"DB{InvoiceID}";
                default:
                    return $"INV{InvoiceID}";
            }
        }
        private string GetNextInvoiceNumber()
        {
            return "INV/2022/9/26/2";
        }

        private List<LineItem> CastInvoiceItemModelListToLineItemList(
            List<InvoiceItemModel> invoiceItemModels)
        {
            //Index =  1, ProductName = "Item", Quantity = 1, NetPrice = 100, Tax = 15, TaxCategory = "S",

            List<LineItem> items = new List<LineItem>();
            int indexCounter = 1;

            foreach (var model in invoiceItemModels)
            {
                var lineItem = new LineItem
                {
                    Id = model.Id,
                    //Index = int.Parse(model.Id.Substring(1,3)),
                    Index = indexCounter,
                    ProductName = model.Name,
                    Quantity = double.Parse(model.Qty),
                    NetPrice = double.Parse(model.NetValue),
                    Tax = double.Parse(model.VATPercentage),
                    LineDiscount = double.Parse(model.TotalDiscount)
                };

                items.Add(lineItem);
                indexCounter++;
            }

            return items;
        }
        #endregion

    }
}
