@model List<ZatcaWebMvc.Models.CertificateCreationResponse>
@{
    ViewData["Title"] = "Certificates";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="bi bi-shield-check me-2"></i>Certificate Management
            </h1>
            <p class="text-muted mb-0">Manage your ZATCA certificates and compliance</p>
        </div>
        <div>
            <a asp-action="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i>Create New Certificate
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>@TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (ViewBag.ErrorMessage != null)
    {
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>@ViewBag.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Certificates Grid -->
    @if (Model.Any())
    {
        <div class="row">
            @foreach (var certificate in Model)
            {
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-shield-check me-1"></i>Certificate #@certificate.RequestId
                            </h6>
                            @{
                                var statusClass = certificate.Status switch
                                {
                                    "Active" => "bg-success",
                                    "Pending" => "bg-warning",
                                    "Expired" => "bg-danger",
                                    "Revoked" => "bg-secondary",
                                    _ => "bg-info"
                                };
                            }
                            <span class="badge @statusClass">@certificate.Status</span>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted">Created Date</small>
                                <div class="fw-bold">@certificate.CreatedDate.ToString("dd/MM/yyyy HH:mm")</div>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted">Expiry Date</small>
                                <div class="fw-bold">
                                    @certificate.ExpiryDate.ToString("dd/MM/yyyy")
                                    @{
                                        var daysUntilExpiry = (certificate.ExpiryDate - DateTime.Now).Days;
                                    }
                                    @if (daysUntilExpiry <= 30 && daysUntilExpiry > 0)
                                    {
                                        <span class="badge bg-warning ms-2">
                                            <i class="bi bi-exclamation-triangle me-1"></i>Expires in @daysUntilExpiry days
                                        </span>
                                    }
                                    else if (daysUntilExpiry <= 0)
                                    {
                                        <span class="badge bg-danger ms-2">
                                            <i class="bi bi-x-circle me-1"></i>Expired
                                        </span>
                                    }
                                </div>
                            </div>

                            @if (!string.IsNullOrEmpty(certificate.BinarySecurityToken))
                            {
                                <div class="mb-3">
                                    <small class="text-muted">Security Token</small>
                                    <div class="font-monospace small text-truncate">
                                        @certificate.BinarySecurityToken.Substring(0, Math.Min(50, certificate.BinarySecurityToken.Length))...
                                    </div>
                                </div>
                            }
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between">
                                <a asp-action="Details" asp-route-id="@certificate.RequestId" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                
                                @if (certificate.Status == "Active" && (certificate.ExpiryDate - DateTime.Now).Days <= 30)
                                {
                                    <form asp-action="Renew" method="post" style="display: inline;">
                                        <input type="hidden" name="id" value="@certificate.RequestId" />
                                        <button type="submit" class="btn btn-warning btn-sm" 
                                                onclick="return confirm('Are you sure you want to renew this certificate?')">
                                            <i class="bi bi-arrow-clockwise me-1"></i>Renew
                                        </button>
                                    </form>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Statistics Summary -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-bar-chart me-1"></i>Certificate Statistics
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="border-end">
                                    <div class="h4 mb-0 text-primary">@Model.Count</div>
                                    <small class="text-muted">Total Certificates</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <div class="h4 mb-0 text-success">@Model.Count(c => c.Status == "Active")</div>
                                    <small class="text-muted">Active</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <div class="h4 mb-0 text-warning">@Model.Count(c => (c.ExpiryDate - DateTime.Now).Days <= 30 && (c.ExpiryDate - DateTime.Now).Days > 0)</div>
                                    <small class="text-muted">Expiring Soon</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="h4 mb-0 text-danger">@Model.Count(c => c.ExpiryDate <= DateTime.Now)</div>
                                <small class="text-muted">Expired</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- Empty State -->
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="bi bi-shield-x display-1 text-muted"></i>
            </div>
            <h4 class="text-muted">No Certificates Found</h4>
            <p class="text-muted mb-4">You haven't created any certificates yet. Create your first certificate to get started with ZATCA compliance.</p>
            <a asp-action="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i>Create Your First Certificate
            </a>
        </div>
    }
</div>

@section Scripts {
    <script>
        // Auto-refresh certificate status every 30 seconds
        setInterval(function() {
            $('.card').each(function() {
                const card = $(this);
                const requestId = card.find('input[name="id"]').val();
                
                if (requestId) {
                    $.get(`/Certificate/GetStatus/${requestId}`)
                        .done(function(data) {
                            if (data.success) {
                                // Update status badge if needed
                                const statusBadge = card.find('.badge');
                                if (statusBadge.text() !== data.status) {
                                    statusBadge.text(data.status);
                                    // Update badge color based on status
                                    statusBadge.removeClass('bg-success bg-warning bg-danger bg-secondary bg-info');
                                    switch(data.status) {
                                        case 'Active':
                                            statusBadge.addClass('bg-success');
                                            break;
                                        case 'Pending':
                                            statusBadge.addClass('bg-warning');
                                            break;
                                        case 'Expired':
                                            statusBadge.addClass('bg-danger');
                                            break;
                                        case 'Revoked':
                                            statusBadge.addClass('bg-secondary');
                                            break;
                                        default:
                                            statusBadge.addClass('bg-info');
                                    }
                                }
                            }
                        });
                }
            });
        }, 30000); // 30 seconds
    </script>
}
