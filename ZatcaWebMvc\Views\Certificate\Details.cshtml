@model ZatcaWebMvc.Models.CertificateCreationResponse
@{
    ViewData["Title"] = "Certificate Details";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-action="Index">Certificates</a></li>
                    <li class="breadcrumb-item active">Certificate #@Model.RequestId</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="bi bi-shield-check me-2"></i>Certificate Details
            </h1>
            <p class="text-muted mb-0">Certificate #@Model.RequestId</p>
        </div>
        <div>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Back to List
            </a>
            @{
                var daysUntilExpiry = (Model.ExpiryDate - DateTime.Now).Days;
            }
            @if (Model.Status == "Active" && daysUntilExpiry <= 30)
            {
                <form asp-action="Renew" method="post" style="display: inline;">
                    <input type="hidden" name="id" value="@Model.RequestId" />
                    <button type="submit" class="btn btn-warning ms-2" 
                            onclick="return confirm('Are you sure you want to renew this certificate?')">
                        <i class="bi bi-arrow-clockwise me-1"></i>Renew Certificate
                    </button>
                </form>
            }
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>@TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="row">
        <!-- Certificate Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-info-circle me-1"></i>Certificate Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Request ID:</td>
                                    <td>@Model.RequestId</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Created Date:</td>
                                    <td>@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Expiry Date:</td>
                                    <td>
                                        @Model.ExpiryDate.ToString("dd/MM/yyyy")
                                        @if (daysUntilExpiry <= 30 && daysUntilExpiry > 0)
                                        {
                                            <span class="badge bg-warning ms-2">
                                                <i class="bi bi-exclamation-triangle me-1"></i>Expires in @daysUntilExpiry days
                                            </span>
                                        }
                                        else if (daysUntilExpiry <= 0)
                                        {
                                            <span class="badge bg-danger ms-2">
                                                <i class="bi bi-x-circle me-1"></i>Expired
                                            </span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Status:</td>
                                    <td>
                                        @{
                                            var statusClass = Model.Status switch
                                            {
                                                "Active" => "bg-success",
                                                "Pending" => "bg-warning",
                                                "Expired" => "bg-danger",
                                                "Revoked" => "bg-secondary",
                                                _ => "bg-info"
                                            };
                                        }
                                        <span class="badge @statusClass fs-6" id="certificateStatus">@Model.Status</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Certificate Security Details -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-shield-lock me-1"></i>Security Details
                    </h6>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(Model.BinarySecurityToken))
                    {
                        <div class="mb-3">
                            <label class="form-label fw-bold">Binary Security Token:</label>
                            <div class="input-group">
                                <textarea class="form-control font-monospace" rows="4" readonly>@Model.BinarySecurityToken</textarea>
                                <button class="btn btn-outline-secondary" type="button" 
                                        onclick="copyToClipboard('@Html.Raw(Html.Encode(Model.BinarySecurityToken))')" title="Copy">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </div>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Secret))
                    {
                        <div class="mb-3">
                            <label class="form-label fw-bold">Secret:</label>
                            <div class="input-group">
                                <input type="password" class="form-control font-monospace" 
                                       value="@Model.Secret" readonly id="secretField" />
                                <button class="btn btn-outline-secondary" type="button" 
                                        onclick="toggleSecret()" title="Show/Hide">
                                    <i class="bi bi-eye" id="secretToggleIcon"></i>
                                </button>
                                <button class="btn btn-outline-secondary" type="button" 
                                        onclick="copyToClipboard('@Html.Raw(Html.Encode(Model.Secret))')" title="Copy">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </div>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Csr))
                    {
                        <div class="mb-3">
                            <label class="form-label fw-bold">Certificate Signing Request (CSR):</label>
                            <div class="input-group">
                                <textarea class="form-control font-monospace" rows="6" readonly>@Model.Csr</textarea>
                                <button class="btn btn-outline-secondary" type="button" 
                                        onclick="copyToClipboard('@Html.Raw(Html.Encode(Model.Csr))')" title="Copy">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </div>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.PrivateKey))
                    {
                        <div class="mb-3">
                            <label class="form-label fw-bold">Private Key:</label>
                            <div class="input-group">
                                <textarea class="form-control font-monospace" rows="6" readonly>@Model.PrivateKey</textarea>
                                <button class="btn btn-outline-secondary" type="button" 
                                        onclick="copyToClipboard('@Html.Raw(Html.Encode(Model.PrivateKey))')" title="Copy">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </div>
                            <div class="form-text text-warning">
                                <i class="bi bi-exclamation-triangle me-1"></i>
                                Keep this private key secure and never share it publicly.
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Status and Actions -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-activity me-1"></i>Certificate Status
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="bi bi-shield-check display-4 text-primary"></i>
                    </div>
                    <h5 class="mb-3">Certificate #@Model.RequestId</h5>
                    <div class="mb-3">
                        <span class="badge @statusClass fs-6" id="statusBadge">@Model.Status</span>
                    </div>
                    
                    @if (daysUntilExpiry > 0)
                    {
                        <div class="mb-3">
                            <small class="text-muted">Days until expiry</small>
                            <div class="h4 @(daysUntilExpiry <= 30 ? "text-warning" : "text-success")">@daysUntilExpiry</div>
                        </div>
                    }
                    else
                    {
                        <div class="mb-3">
                            <small class="text-danger">Certificate Expired</small>
                            <div class="h4 text-danger">@Math.Abs(daysUntilExpiry) days ago</div>
                        </div>
                    }

                    <hr>

                    <div class="d-grid gap-2">
                        @if (Model.Status == "Active" && daysUntilExpiry <= 30)
                        {
                            <button type="button" class="btn btn-warning" id="renewBtn">
                                <i class="bi bi-arrow-clockwise me-1"></i>Renew Certificate
                            </button>
                        }
                        <button type="button" class="btn btn-outline-primary" onclick="refreshStatus()">
                            <i class="bi bi-arrow-clockwise me-1"></i>Refresh Status
                        </button>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-lightning me-1"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="downloadCertificate()">
                            <i class="bi bi-download me-1"></i>Download Certificate
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="printCertificate()">
                            <i class="bi bi-printer me-1"></i>Print Details
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showToast('Copied to clipboard!', 'success');
            });
        }

        function toggleSecret() {
            const field = document.getElementById('secretField');
            const icon = document.getElementById('secretToggleIcon');
            
            if (field.type === 'password') {
                field.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                field.type = 'password';
                icon.className = 'bi bi-eye';
            }
        }

        function refreshStatus() {
            const refreshBtn = document.querySelector('button[onclick="refreshStatus()"]');
            const originalText = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1 spinner-border spinner-border-sm"></i>Refreshing...';
            refreshBtn.disabled = true;

            fetch('/Certificate/GetStatus/@(Model.RequestId)')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update status displays
                        document.getElementById('certificateStatus').textContent = data.status;
                        document.getElementById('statusBadge').textContent = data.status;
                        
                        // Update badge colors
                        const badges = document.querySelectorAll('#certificateStatus, #statusBadge');
                        badges.forEach(badge => {
                            badge.className = badge.className.replace(/bg-\w+/, '');
                            switch(data.status) {
                                case 'Active':
                                    badge.classList.add('bg-success');
                                    break;
                                case 'Pending':
                                    badge.classList.add('bg-warning');
                                    break;
                                case 'Expired':
                                    badge.classList.add('bg-danger');
                                    break;
                                case 'Revoked':
                                    badge.classList.add('bg-secondary');
                                    break;
                                default:
                                    badge.classList.add('bg-info');
                            }
                        });
                        
                        showToast('Status updated successfully!', 'success');
                    } else {
                        showToast('Error refreshing status: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('Error refreshing status', 'error');
                })
                .finally(() => {
                    refreshBtn.innerHTML = originalText;
                    refreshBtn.disabled = false;
                });
        }

        function downloadCertificate() {
            // Create a downloadable file with certificate details
            const content = 'Certificate Details\n' +
                'Request ID: @(Model.RequestId)\n' +
                'Status: @(Model.Status)\n' +
                'Created: @(Model.CreatedDate.ToString("dd/MM/yyyy HH:mm"))\n' +
                'Expires: @(Model.ExpiryDate.ToString("dd/MM/yyyy"))\n\n' +
                'Binary Security Token:\n@(Model.BinarySecurityToken)\n\n' +
                'CSR:\n@(Model.Csr)';

            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'certificate-@(Model.RequestId).txt';
            a.click();
            window.URL.revokeObjectURL(url);
        }

        function printCertificate() {
            window.print();
        }

        function showToast(message, type) {
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0 position-fixed top-0 end-0 m-3`;
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        }

        // Renew button handler
        document.addEventListener('DOMContentLoaded', function() {
            const renewBtn = document.getElementById('renewBtn');
            if (renewBtn) {
                renewBtn.addEventListener('click', function() {
                    if (confirm('Are you sure you want to renew this certificate?')) {
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.action = '/Certificate/Renew';
                        
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'id';
                        input.value = '@(Model.RequestId)';
                        
                        form.appendChild(input);
                        document.body.appendChild(form);
                        form.submit();
                    }
                });
            }
        });
    </script>
}
