using Microsoft.AspNetCore.Mvc;
using ZatcaWebMvc.Models;
using ZatcaWebMvc.Services;

namespace ZatcaWebMvc.Controllers
{
    /// <summary>
    /// Controller for managing sellers
    /// </summary>
    public class SellerController : Controller
    {
        private readonly IZatcaApiService _zatcaApiService;
        private readonly ILogger<SellerController> _logger;

        public SellerController(IZatcaApiService zatcaApiService, ILogger<SellerController> logger)
        {
            _zatcaApiService = zatcaApiService;
            _logger = logger;
        }

        /// <summary>
        /// Display list of all sellers
        /// </summary>
        public async Task<IActionResult> Index()
        {
            try
            {
                var response = await _zatcaApiService.GetAllSellersAsync();
                
                if (response.Success && response.Data != null)
                {
                    return View(response.Data);
                }
                
                ViewBag.ErrorMessage = response.Message;
                return View(new List<SellerResponse>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading sellers");
                ViewBag.ErrorMessage = "An error occurred while loading sellers.";
                return View(new List<SellerResponse>());
            }
        }

        /// <summary>
        /// Display seller details
        /// </summary>
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var response = await _zatcaApiService.GetSellerAsync(id);
                
                if (response.Success && response.Data != null)
                {
                    return View(response.Data);
                }
                
                ViewBag.ErrorMessage = response.Message;
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading seller {SellerId}", id);
                ViewBag.ErrorMessage = "An error occurred while loading the seller.";
                return NotFound();
            }
        }

        /// <summary>
        /// Display create seller form
        /// </summary>
        public IActionResult Create()
        {
            var model = new SellerCreateRequest
            {
                CountryCode = "SA",
                IdentityType = "CRN"
            };
            return View(model);
        }

        /// <summary>
        /// Handle seller creation
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(SellerCreateRequest model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var response = await _zatcaApiService.CreateSellerAsync(model);
                
                if (response.Success)
                {
                    TempData["SuccessMessage"] = "Seller created successfully!";
                    return RedirectToAction(nameof(Index));
                }
                
                // Add API errors to ModelState
                foreach (var error in response.Errors)
                {
                    ModelState.AddModelError("", error);
                }
                
                ViewBag.ErrorMessage = response.Message;
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating seller");
                ModelState.AddModelError("", "An error occurred while creating the seller.");
                return View(model);
            }
        }

        /// <summary>
        /// Display edit seller form
        /// </summary>
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var response = await _zatcaApiService.GetSellerAsync(id);
                
                if (response.Success && response.Data != null)
                {
                    var model = new SellerCreateRequest
                    {
                        SellerTRN = response.Data.SellerTRN,
                        SellerName = response.Data.SellerName,
                        StreetName = response.Data.StreetName,
                        CityName = response.Data.CityName,
                        DistrictName = response.Data.DistrictName,
                        BuildingNumber = response.Data.BuildingNumber,
                        PostalCode = response.Data.PostalCode,
                        AdditionalStreetAddress = response.Data.AdditionalStreetAddress,
                        IdentityType = response.Data.IdentityType,
                        IdentityNumber = response.Data.IdentityNumber,
                        CountryCode = response.Data.CountryCode,
                        Email = response.Data.Email,
                        Phone = response.Data.Phone,
                        BusinessCategory = response.Data.BusinessCategory
                    };
                    
                    ViewBag.SellerId = id;
                    return View(model);
                }
                
                ViewBag.ErrorMessage = response.Message;
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading seller {SellerId} for edit", id);
                ViewBag.ErrorMessage = "An error occurred while loading the seller.";
                return NotFound();
            }
        }

        /// <summary>
        /// Handle seller update
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, SellerCreateRequest model)
        {
            if (!ModelState.IsValid)
            {
                ViewBag.SellerId = id;
                return View(model);
            }

            try
            {
                var response = await _zatcaApiService.UpdateSellerAsync(id, model);
                
                if (response.Success)
                {
                    TempData["SuccessMessage"] = "Seller updated successfully!";
                    return RedirectToAction(nameof(Index));
                }
                
                // Add API errors to ModelState
                foreach (var error in response.Errors)
                {
                    ModelState.AddModelError("", error);
                }
                
                ViewBag.ErrorMessage = response.Message;
                ViewBag.SellerId = id;
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating seller {SellerId}", id);
                ModelState.AddModelError("", "An error occurred while updating the seller.");
                ViewBag.SellerId = id;
                return View(model);
            }
        }

        /// <summary>
        /// Display delete confirmation
        /// </summary>
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var response = await _zatcaApiService.GetSellerAsync(id);
                
                if (response.Success && response.Data != null)
                {
                    return View(response.Data);
                }
                
                ViewBag.ErrorMessage = response.Message;
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading seller {SellerId} for delete", id);
                ViewBag.ErrorMessage = "An error occurred while loading the seller.";
                return NotFound();
            }
        }

        /// <summary>
        /// Handle seller deletion
        /// </summary>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var response = await _zatcaApiService.DeleteSellerAsync(id);
                
                if (response.Success)
                {
                    TempData["SuccessMessage"] = "Seller deleted successfully!";
                    return RedirectToAction(nameof(Index));
                }
                
                ViewBag.ErrorMessage = response.Message;
                
                // Reload the seller for the view
                var sellerResponse = await _zatcaApiService.GetSellerAsync(id);
                if (sellerResponse.Success && sellerResponse.Data != null)
                {
                    return View(sellerResponse.Data);
                }
                
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting seller {SellerId}", id);
                TempData["ErrorMessage"] = "An error occurred while deleting the seller.";
                return RedirectToAction(nameof(Index));
            }
        }
    }
}
