﻿using Application.Contracts.IRepository;
using Domain.Entities.A7MD;
using Microsoft.EntityFrameworkCore;
using Persistence.Data.A7MD;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Persistence.Repositories
{
    //public class A7MDRepository : IA7MDRepository
    //{
    //    private readonly A7MDDbContext _a7mdDbContext;

    //    public A7MDRepository(A7MDDbContext a7mdDbContext)
    //    {
    //        _a7mdDbContext = a7mdDbContext;
    //    }
    //    /// <summary>
    //    /// 
    //    /// </summary>
    //    /// <returns></returns>
    //    public async Task<IReadOnlyList<InvoicesToZATCA>> GetA7MDNotSentInvoicesAsync()
    //    {
    //        return await _a7mdDbContext.InvoicesToZATCAs
    //            .Where(x=>x.IsSent == false)
    //            .ToListAsync();
    //    }
    //    /// <summary>
    //    /// 
    //    /// </summary>
    //    /// <param name="invoices"></param>
    //    public async Task UpdateA7MDSentInvoicesAsync(IReadOnlyList<InvoicesToZATCA> invoices)
    //    {
    //        _a7mdDbContext.InvoicesToZATCAs
    //            .UpdateRange(invoices);
    //        await _a7mdDbContext.SaveChangesAsync();
    //    }
    //}
}
