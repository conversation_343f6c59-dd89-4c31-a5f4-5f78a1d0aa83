/* ===== ZATCA E-Invoice Modern Design System ===== */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* ===== CSS Custom Properties (Design Tokens) ===== */
:root {
  /* Colors - Primary Palette */
  --color-primary: #2563eb;
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Colors - Secondary Palette */
  --color-secondary: #64748b;
  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;

  /* Colors - Semantic */
  --color-success: #10b981;
  --color-success-light: #d1fae5;
  --color-warning: #f59e0b;
  --color-warning-light: #fef3c7;
  --color-error: #ef4444;
  --color-error-light: #fee2e2;
  --color-info: #06b6d4;
  --color-info-light: #cffafe;

  /* Colors - Neutral */
  --color-white: #ffffff;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* Typography */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', monospace;

  /* Font Sizes */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  --text-4xl: 2.25rem;     /* 36px */
  --text-5xl: 3rem;        /* 48px */

  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;

  /* Spacing */
  --space-1: 0.25rem;      /* 4px */
  --space-2: 0.5rem;       /* 8px */
  --space-3: 0.75rem;      /* 12px */
  --space-4: 1rem;         /* 16px */
  --space-5: 1.25rem;      /* 20px */
  --space-6: 1.5rem;       /* 24px */
  --space-8: 2rem;         /* 32px */
  --space-10: 2.5rem;      /* 40px */
  --space-12: 3rem;        /* 48px */
  --space-16: 4rem;        /* 64px */
  --space-20: 5rem;        /* 80px */

  /* Border Radius */
  --radius-sm: 0.25rem;    /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ===== Base Styles Reset ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--color-gray-900);
  background-color: var(--color-gray-50);
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* ===== Typography System ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  margin: 0 0 var(--space-4) 0;
  color: var(--color-gray-900);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
  margin: 0 0 var(--space-4) 0;
  color: var(--color-gray-700);
  line-height: var(--leading-relaxed);
}

/* ===== Modern Layout Components ===== */
.modern-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.modern-grid {
  display: grid;
  gap: var(--space-6);
}

.modern-flex {
  display: flex;
  gap: var(--space-4);
}

.modern-stack {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

/* ===== Modern Card Component ===== */
.modern-card {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.modern-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.modern-card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-gray-200);
  background: var(--color-white);
}

.modern-card-body {
  padding: var(--space-6);
}

.modern-card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--color-gray-200);
  background: var(--color-gray-50);
}

/* ===== Modern Button System ===== */
.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-family-sans);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: 1;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
}

.modern-btn:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.modern-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Variants */
.modern-btn-primary {
  background: var(--color-primary-600);
  color: var(--color-white);
}

.modern-btn-primary:hover:not(:disabled) {
  background: var(--color-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.modern-btn-secondary {
  background: var(--color-white);
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-300);
}

.modern-btn-secondary:hover:not(:disabled) {
  background: var(--color-gray-50);
  border-color: var(--color-gray-400);
}

.modern-btn-success {
  background: var(--color-success);
  color: var(--color-white);
}

.modern-btn-success:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.modern-btn-danger {
  background: var(--color-error);
  color: var(--color-white);
}

.modern-btn-danger:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Button Sizes */
.modern-btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-xs);
}

.modern-btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-base);
}

/* ===== Modern Form Components ===== */
.modern-form-group {
  margin-bottom: var(--space-6);
}

.modern-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--space-2);
}

.modern-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  font-family: var(--font-family-sans);
  color: var(--color-gray-900);
  background: var(--color-white);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  appearance: none;
}

.modern-input:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px var(--color-primary-100);
}

.modern-input:disabled {
  background: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
}

.modern-input::placeholder {
  color: var(--color-gray-400);
}

/* Floating Label Input */
.modern-floating-group {
  position: relative;
  margin-bottom: var(--space-6);
}

.modern-floating-input {
  width: 100%;
  padding: var(--space-4) var(--space-4) var(--space-3) var(--space-4);
  font-size: var(--text-base);
  color: var(--color-gray-900);
  background: var(--color-white);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  appearance: none;
}

.modern-floating-input:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px var(--color-primary-100);
}

.modern-floating-label {
  position: absolute;
  top: var(--space-4);
  left: var(--space-4);
  font-size: var(--text-base);
  color: var(--color-gray-400);
  transition: all var(--transition-fast);
  pointer-events: none;
  background: var(--color-white);
  padding: 0 var(--space-1);
}

.modern-floating-input:focus + .modern-floating-label,
.modern-floating-input:not(:placeholder-shown) + .modern-floating-label {
  top: -8px;
  left: var(--space-3);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--color-primary-600);
}

/* Select Input */
.modern-select {
  width: 100%;
  padding: var(--space-3) var(--space-10) var(--space-3) var(--space-4);
  font-size: var(--text-base);
  color: var(--color-gray-900);
  background: var(--color-white) url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") no-repeat right var(--space-3) center;
  background-size: 16px 16px;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  appearance: none;
}

.modern-select:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px var(--color-primary-100);
}

/* Checkbox and Radio */
.modern-checkbox,
.modern-radio {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
  font-size: var(--text-sm);
  color: var(--color-gray-700);
}

.modern-checkbox input,
.modern-radio input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.modern-checkbox .checkmark,
.modern-radio .checkmark {
  width: 20px;
  height: 20px;
  background: var(--color-white);
  border: 2px solid var(--color-gray-300);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-radio .checkmark {
  border-radius: var(--radius-full);
}

.modern-checkbox input:checked + .checkmark,
.modern-radio input:checked + .checkmark {
  background: var(--color-primary-600);
  border-color: var(--color-primary-600);
}

.modern-checkbox input:checked + .checkmark::after {
  content: "✓";
  color: var(--color-white);
  font-size: 12px;
  font-weight: var(--font-bold);
}

.modern-radio input:checked + .checkmark::after {
  content: "";
  width: 8px;
  height: 8px;
  background: var(--color-white);
  border-radius: var(--radius-full);
}

/* Checkbox Container and Label */
.modern-checkbox-container {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.modern-checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  font-size: var(--text-sm);
  color: var(--color-gray-700);
  user-select: none;
}

/* ===== Modern Alert Components ===== */
.modern-alert {
  padding: var(--space-4) var(--space-5);
  border-radius: var(--radius-lg);
  border-left: 4px solid;
  margin-bottom: var(--space-4);
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
}

.modern-alert-success {
  background: var(--color-success-light);
  border-left-color: var(--color-success);
  color: #065f46;
}

.modern-alert-warning {
  background: var(--color-warning-light);
  border-left-color: var(--color-warning);
  color: #92400e;
}

.modern-alert-error {
  background: var(--color-error-light);
  border-left-color: var(--color-error);
  color: #991b1b;
}

.modern-alert-info {
  background: var(--color-info-light);
  border-left-color: var(--color-info);
  color: #155e75;
}

.modern-alert-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  margin-top: 2px;
}

.modern-alert-content {
  flex: 1;
}

.modern-alert-title {
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-1);
}

.modern-alert-message {
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
}

/* ===== Modern Navigation ===== */
.modern-navbar {
  background: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.modern-navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.modern-navbar-brand {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--color-primary-600);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.modern-navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  list-style: none;
  margin: 0;
  padding: 0;
}

.modern-navbar-link {
  padding: var(--space-2) var(--space-4);
  color: var(--color-gray-600);
  text-decoration: none;
  font-weight: var(--font-medium);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.modern-navbar-link:hover {
  color: var(--color-primary-600);
  background: var(--color-primary-50);
}

.modern-navbar-link.active {
  color: var(--color-primary-600);
  background: var(--color-primary-100);
}

/* ===== Modern Sidebar Navigation ===== */
.modern-nav-link {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  color: var(--color-gray-700);
  text-decoration: none;
  font-weight: var(--font-medium);
  border-radius: var(--radius-md);
  transition: all var(--transition-base);
  margin-bottom: var(--space-1);
}

.modern-nav-link:hover {
  color: var(--color-primary-600);
  background-color: var(--color-primary-50);
  text-decoration: none;
  transform: translateX(2px);
}

.modern-nav-link-active {
  color: var(--color-primary-600) !important;
  background-color: var(--color-primary-100) !important;
  border-left: 3px solid var(--color-primary-600);
  padding-left: calc(var(--space-4) - 3px);
}

.modern-nav-link i {
  width: 20px;
  text-align: center;
  opacity: 0.7;
}

.modern-nav-link-active i {
  opacity: 1;
}

/* ===== Modern Badge Component ===== */
.modern-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.modern-badge-primary {
  background: var(--color-primary-100);
  color: var(--color-primary-800);
}

.modern-badge-success {
  background: var(--color-success-light);
  color: #065f46;
}

.modern-badge-warning {
  background: var(--color-warning-light);
  color: #92400e;
}

.modern-badge-error {
  background: var(--color-error-light);
  color: #991b1b;
}

.modern-badge-gray {
  background: var(--color-gray-100);
  color: var(--color-gray-800);
}

/* ===== Modern Loading States ===== */
.modern-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-gray-200);
  border-top: 2px solid var(--color-primary-600);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.modern-skeleton {
  background: linear-gradient(90deg, var(--color-gray-200) 25%, var(--color-gray-100) 50%, var(--color-gray-200) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* ===== Utility Classes ===== */
/* Spacing */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

/* Text */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }

.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Colors */
.text-gray-500 { color: var(--color-gray-500); }
.text-gray-600 { color: var(--color-gray-600); }
.text-gray-700 { color: var(--color-gray-700); }
.text-gray-900 { color: var(--color-gray-900); }
.text-primary { color: var(--color-primary-600); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

/* Background */
.bg-white { background-color: var(--color-white); }
.bg-gray-50 { background-color: var(--color-gray-50); }
.bg-gray-100 { background-color: var(--color-gray-100); }
.bg-primary { background-color: var(--color-primary-600); }

/* Flexbox */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }

/* Grid */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

/* Responsive Design */
@media (max-width: 768px) {
  .modern-container {
    padding: 0 var(--space-4);
  }

  .modern-navbar-container {
    padding: 0 var(--space-4);
  }

  .modern-grid {
    gap: var(--space-4);
  }

  .grid-cols-2,
  .grid-cols-3 {
    grid-template-columns: 1fr;
  }

  .modern-btn {
    width: 100%;
    justify-content: center;
  }
}
