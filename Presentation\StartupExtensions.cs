using Application.Contracts.IRepository;
using Application.Contracts.IServices;
using Application.Models.Zatca;
using Application.Services;
using EInvoiceKSADemo.Helpers.Zatca;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using Persistence;
using Persistence.Data;
using Persistence.Repositories;
using Presentation.Filters;
using System.Reflection;

namespace Presentation
{
    public static class StartupExtensions
    {
        public static WebApplication ConfigureServices(this WebApplicationBuilder builder)
        {
            builder.Services.AddControllersWithViews();
            
            SharedData.APIUrl = builder.Configuration["ZatcaSettings:SimulationUrl"];

            // Database configuration - optional for testing
            var connectionString = builder.Configuration.GetConnectionString("ZatcaDemoDB");
            if (!string.IsNullOrEmpty(connectionString))
            {
                builder.Services.AddDbContext<ApplicationDbContext>(options =>
                {
                    options.UseSqlServer(connectionString);
                });
            }

            // builder.Services.AddPersistenceServices(builder.Configuration); // Commented out for testing without database
            // builder.Services.AddZatcaServices(builder.Configuration); // Commented out for testing without database
            // builder.Services.AddScoped<ICertificateCreationService, CertificateCreationService>(); // Commented out for testing without database
            // builder.Services.AddScoped<IZatcaInvoiceSender, ZatcaInvoiceSender>(); // Commented out for testing without database

            //builder.Services.AddInfrastructureServices(builder.Configuration);
            
            builder.Services.AddApiVersioning(options =>
            {
                options.AssumeDefaultVersionWhenUnspecified = true;
                options.DefaultApiVersion = new ApiVersion(1, 0);
                options.ReportApiVersions = true;
            });

            // Add Swagger services
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = "ZATCA E-Invoice API",
                    Version = "v1.0",
                    Description = "API for ZATCA (Zakat, Tax and Customs Authority) E-Invoice integration in Saudi Arabia",
                    Contact = new OpenApiContact
                    {
                        Name = "API Support",
                        Email = "<EMAIL>"
                    }
                });

                // Include XML comments for better documentation
                var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                if (File.Exists(xmlPath))
                {
                    c.IncludeXmlComments(xmlPath);
                }

                // Add security definition for API key or bearer token if needed
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer"
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        new string[] {}
                    }
                });

                // Configure API versioning for Swagger
                c.DocInclusionPredicate((version, desc) => true);
                c.OperationFilter<ApiVersionOperationFilter>();
            });

            return builder.Build();
        }
    }
}
