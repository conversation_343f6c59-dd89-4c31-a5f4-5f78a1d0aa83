﻿using Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Models.Zatca
{
    public class InputCsrModel
    {
        public string CSR { get; set; }
        public string PrivateKey { get; set; }
    }

    public class CertificateDetails
    {
        public CertificateDetails() { }
        public CertificateDetails(CertificateSettings certificateSettings) 
        {
            UserName = certificateSettings.UserName;
            Secret = certificateSettings.Secret;
            Certificate = certificateSettings.Certificate;
            CSR = certificateSettings.Csr;
            PrivateKey = certificateSettings.PrivateKey;
            StartedDate=certificateSettings.StartedDate??DateTime.Now.Date;
            ExpiredDate = certificateSettings.ExpiredDate ?? DateTime.Now.AddYears(2).Date;
        }
        public string UserName { get; set; }
        public string Secret { get; set; }

        public string Certificate { get; set; }

        public string CSR { get; set; }

        public string PrivateKey { get; set; }

        public DateTime StartedDate { get; set; }

        public DateTime ExpiredDate { get; set; }
    }
}
