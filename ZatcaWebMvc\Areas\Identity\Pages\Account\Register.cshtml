﻿@page
@model RegisterModel
@{
    ViewData["Title"] = "Create Account";
    Layout = "_Layout";
}

<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-lg w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="modern-brand-icon mx-auto mb-6">
                <i class="bi bi-person-plus text-primary-600" style="font-size: 3rem;"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Create your account</h1>
            <p class="text-gray-600">Join ZATCA E-Invoice Management System</p>
        </div>

        <!-- Registration Form Card -->
        <div class="modern-card">
            <form id="registerForm" asp-route-returnUrl="@Model.ReturnUrl" method="post" class="space-y-6">
                <div asp-validation-summary="ModelOnly" class="modern-alert modern-alert-error" role="alert" style="display: none;"></div>

                <!-- Name Fields Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- First Name -->
                    <div class="modern-form-group">
                        <div class="modern-floating-input-container">
                            <input asp-for="Input.FirstName"
                                   class="modern-floating-input"
                                   type="text"
                                   autocomplete="given-name"
                                   aria-required="true"
                                   placeholder=" " />
                            <label asp-for="Input.FirstName" class="modern-floating-label">
                                <i class="bi bi-person me-2"></i>First Name
                            </label>
                        </div>
                        <span asp-validation-for="Input.FirstName" class="modern-form-error"></span>
                    </div>

                    <!-- Last Name -->
                    <div class="modern-form-group">
                        <div class="modern-floating-input-container">
                            <input asp-for="Input.LastName"
                                   class="modern-floating-input"
                                   type="text"
                                   autocomplete="family-name"
                                   aria-required="true"
                                   placeholder=" " />
                            <label asp-for="Input.LastName" class="modern-floating-label">
                                <i class="bi bi-person me-2"></i>Last Name
                            </label>
                        </div>
                        <span asp-validation-for="Input.LastName" class="modern-form-error"></span>
                    </div>
                </div>

                <!-- Company Name -->
                <div class="modern-form-group">
                    <div class="modern-floating-input-container">
                        <input asp-for="Input.CompanyName"
                               class="modern-floating-input"
                               type="text"
                               autocomplete="organization"
                               placeholder=" " />
                        <label asp-for="Input.CompanyName" class="modern-floating-label">
                            <i class="bi bi-building me-2"></i>Company Name (Optional)
                        </label>
                    </div>
                    <span asp-validation-for="Input.CompanyName" class="modern-form-error"></span>
                </div>

                <!-- Email Field -->
                <div class="modern-form-group">
                    <div class="modern-floating-input-container">
                        <input asp-for="Input.Email"
                               class="modern-floating-input"
                               type="email"
                               autocomplete="email"
                               aria-required="true"
                               placeholder=" " />
                        <label asp-for="Input.Email" class="modern-floating-label">
                            <i class="bi bi-envelope me-2"></i>Email Address
                        </label>
                    </div>
                    <span asp-validation-for="Input.Email" class="modern-form-error"></span>
                </div>

                <!-- Password Field -->
                <div class="modern-form-group">
                    <div class="modern-floating-input-container">
                        <input asp-for="Input.Password"
                               class="modern-floating-input"
                               type="password"
                               autocomplete="new-password"
                               aria-required="true"
                               placeholder=" " />
                        <label asp-for="Input.Password" class="modern-floating-label">
                            <i class="bi bi-lock me-2"></i>Password
                        </label>
                    </div>
                    <span asp-validation-for="Input.Password" class="modern-form-error"></span>
                    <div class="text-xs text-gray-500 mt-1">
                        Password must be at least 6 characters long and contain uppercase, lowercase, and numbers.
                    </div>
                </div>

                <!-- Confirm Password Field -->
                <div class="modern-form-group">
                    <div class="modern-floating-input-container">
                        <input asp-for="Input.ConfirmPassword"
                               class="modern-floating-input"
                               type="password"
                               autocomplete="new-password"
                               aria-required="true"
                               placeholder=" " />
                        <label asp-for="Input.ConfirmPassword" class="modern-floating-label">
                            <i class="bi bi-shield-check me-2"></i>Confirm Password
                        </label>
                    </div>
                    <span asp-validation-for="Input.ConfirmPassword" class="modern-form-error"></span>
                </div>

                <!-- Submit Button -->
                <button id="registerSubmit" type="submit" class="modern-btn modern-btn-primary w-full modern-btn-lg">
                    <i class="bi bi-person-plus me-2"></i>
                    Create Account
                </button>

                <!-- Links -->
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        Already have an account?
                        <a asp-page="./Login" asp-route-returnUrl="@Model.ReturnUrl" class="text-primary-600 hover:text-primary-500 font-medium">
                            Sign in here
                        </a>
                    </p>
                </div>
            </form>
        </div>

        @if ((Model.ExternalLogins?.Count ?? 0) > 0)
        {
            <!-- External Login Section -->
            <div class="modern-card">
                <div class="text-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Or register with</h3>
                </div>
                <form id="external-account" asp-page="./ExternalLogin" asp-route-returnUrl="@Model.ReturnUrl" method="post">
                    <div class="space-y-3">
                        @foreach (var provider in Model.ExternalLogins!)
                        {
                            <button type="submit"
                                    class="modern-btn modern-btn-secondary w-full"
                                    name="provider"
                                    value="@provider.Name"
                                    title="Register using your @provider.DisplayName account">
                                @provider.DisplayName
                            </button>
                        }
                    </div>
                </form>
            </div>
        }

        <!-- Footer -->
        <div class="text-center">
            <p class="text-xs text-gray-500">
                By creating an account, you agree to our terms of service and privacy policy.
            </p>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
