using EInvoiceKSADemo.Helpers.Zatca.Constants;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Serialization;
using System.Xml.Xsl;

namespace EInvoiceKSADemo.Helpers.Zatca
{
    internal class ZatcaUtility
    {
        /// <summary>
        /// Retrieves the inner text of the XML node specified by the XPath.
        /// </summary>
        /// <param name="doc">The XML document to search.</param>
        /// <param name="xPath">The XPath query to select the node.</param>
        /// <returns>Returns the inner text of the selected XML node or an empty string if the node is not found.</returns>
        public static string GetNodeInnerText(XmlDocument doc, string xPath)
        {
            XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
            nsmgr.AddNamespace("cac", "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2");
            nsmgr.AddNamespace("cbc", "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2");
            nsmgr.AddNamespace("ext", "urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2");
            XmlNode titleNode = doc.SelectSingleNode(xPath, nsmgr);
            if (titleNode != null)
            {
                return titleNode.InnerText;
            }
            return "";
        }

        /// <summary>
        /// Retrieves the canonical XML of the node specified by the XPath.
        /// </summary>
        /// <param name="doc">The XML document to search.</param>
        /// <param name="xPath">The XPath query to select the node.</param>
        /// <returns>Returns the canonical XML string of the selected node or an empty string if the node is not found.</returns>
        public static string GetNodeInnerXML(XmlDocument doc, string xPath)
        {
            XmlNode titleNode = doc.SelectSingleNode(xPath);
            if (titleNode != null)
            {
                string canonXml = "";
                using (MemoryStream msIn = new MemoryStream(Encoding.UTF8.GetBytes(titleNode.OuterXml)))
                {
                    XmlDsigC14NTransform t = new XmlDsigC14NTransform(false);
                    t.LoadInput(msIn);
                    MemoryStream o = t.GetOutput() as MemoryStream;
                    canonXml = Encoding.UTF8.GetString(o.ToArray());
                }
                return canonXml.Replace("></ds:DigestMethod>", "/>");
            }
            return "";
        }

        /// <summary>
        /// Sets the inner text of the XML node specified by the XPath.
        /// </summary>
        /// <param name="doc">The XML document to modify.</param>
        /// <param name="xPath">The XPath query to select the node.</param>
        /// <param name="value">The value to set as the inner text of the node.</param>
        public static void SetNodeValue(XmlDocument doc, string xPath, string value)
        {
            XmlNode titleNode = doc.SelectSingleNode(xPath);
            if (titleNode != null)
            {
                titleNode.InnerText = value;
            }
        }

        /// <summary>
        /// Retrieves the value of the specified attribute from the XML node specified by the XPath.
        /// </summary>
        /// <param name="doc">The XML document to search.</param>
        /// <param name="xPath">The XPath query to select the node.</param>
        /// <param name="attributeName">The name of the attribute to retrieve the value from.</param>
        /// <returns>Returns the value of the specified attribute or an empty string if the attribute or node is not found.</returns>
        public static string GetNodeAttributeValue(XmlDocument doc, string xPath, string attributeName)
        {
            XmlNode titleNode = doc.SelectSingleNode(xPath);
            if (titleNode != null)
            {
                return titleNode.Attributes[attributeName].Value;
            }
            return "";
        }

        /// <summary>
        /// Applies the specified XSLT transformation to the XML file and returns the result as a string.
        /// </summary>
        /// <param name="xmlFilePath">The path to the XML file to transform.</param>
        /// <param name="xsltFilePath">The path to the XSLT file to use for the transformation.</param>
        /// <returns>Returns the transformed XML as a string.</returns>
        public static string ApplyXSLT(string xmlFilePath, string xsltFilePath)
        {
            StringBuilder output = new StringBuilder();
            XmlWriterSettings writerSettings = new XmlWriterSettings();
            writerSettings.OmitXmlDeclaration = true;
            writerSettings.Encoding = Encoding.UTF8;
            writerSettings.Indent = false;
            XmlWriter transformedData = XmlWriter.Create(output, writerSettings);
            try
            {
                XmlReader xmlReader = XmlReader.Create(ReadInternalEmbededResourceStream(xsltFilePath));
                XslCompiledTransform xsltTransform = new XslCompiledTransform();
                xsltTransform.Load(xmlReader);
                XmlReader input = XmlReader.Create(new StringReader(xmlFilePath));
                xsltTransform.Transform(input, transformedData);
            }
            finally
            {
                transformedData.Dispose();
            }
            return output.ToString();
        }

        /// <summary>
        /// Applies the specified XSLT transformation to the given XML string and returns the result as a string.
        /// </summary>
        /// <param name="xml">The XML string to transform.</param>
        /// <param name="xsltFilePath">The path to the XSLT file to use for the transformation.</param>
        /// <returns>Returns the transformed XML as a string.</returns>
        public static string ApplyXSLTPassingXML(string xml, string xsltFilePath)
        {
            StringBuilder output = new StringBuilder();
            XmlWriterSettings writerSettings = new XmlWriterSettings();
            writerSettings.OmitXmlDeclaration = true;
            writerSettings.Encoding = Encoding.UTF8;
            writerSettings.Indent = true;
            writerSettings.ConformanceLevel = ConformanceLevel.Auto;
            XmlWriter transformedData = XmlWriter.Create(output, writerSettings);
            try
            {
                XmlReader xslReader = XmlReader.Create(ReadInternalEmbededResourceStream(xsltFilePath));
                XmlReader xmlReader = XmlReader.Create(new StringReader(xml));
                xmlReader.Read();
                XslCompiledTransform xsltTransform = new XslCompiledTransform();
                xsltTransform.Load(xslReader);
                xsltTransform.Transform(xmlReader, transformedData);
            }
            finally
            {
                transformedData.Dispose();
            }
            return output.ToString();
        }

        /// <summary>
        /// Computes the SHA-256 hash of the specified value and returns it as a byte array.
        /// </summary>
        /// <param name="value">The input string to hash.</param>
        /// <returns>Returns the SHA-256 hash as a byte array.</returns>
        public static byte[] Sha256_hashAsBytes(string value)
        {
            using SHA256 hash = SHA256.Create();
            return hash.ComputeHash(Encoding.UTF8.GetBytes(value));
        }

        /// <summary>
        /// Computes the SHA-256 hash of the specified value and returns it as a hexadecimal string.
        /// </summary>
        /// <param name="rawData">The input string to hash.</param>
        /// <returns>Returns the SHA-256 hash as a hexadecimal string.</returns>
        public static string Sha256_hashAsString(string rawData)
        {
            StringBuilder Sb = new StringBuilder();
            using (SHA256 hash = SHA256.Create())
            {
                byte[] result = hash.ComputeHash(Encoding.UTF8.GetBytes(rawData));
                foreach (byte b in result)
                {
                    Sb.Append(b.ToString("x2"));
                }
            }
            return Sb.ToString();
        }

        /// <summary>
        /// Encodes the specified string to a Base64 string.
        /// </summary>
        /// <param name="toEncode">The string to encode.</param>
        /// <returns>Returns the Base64 encoded string.</returns>
        public static string ToBase64Encode(string toEncode)
        {
            byte[] toEncodeAsBytes = Encoding.UTF8.GetBytes(toEncode);
            return Convert.ToBase64String(toEncodeAsBytes);
        }

        /// <summary>
        /// Encodes the specified byte array to a Base64 string.
        /// </summary>
        /// <param name="value">The byte array to encode.</param>
        /// <returns>Returns the Base64 encoded string or null if the input is null.</returns>
        public static string ToBase64Encode(byte[] value)
        {
            if (value == null)
            {
                return null;
            }
            return Convert.ToBase64String(value);
        }

        /// <summary>
        /// Decodes the specified Base64 encoded string to a byte array.
        /// </summary>
        /// <param name="base64EncodedText">The Base64 encoded string to decode.</param>
        /// <returns>Returns the decoded byte array or null if the input is null or empty.</returns>
        public static byte[] ToBase64DecodeAsBinary(string base64EncodedText)
        {
            if (string.IsNullOrEmpty(base64EncodedText))
            {
                return null;
            }
            return Convert.FromBase64String(base64EncodedText);
        }

        /// <summary>
        /// Retrieves the invoice type from the XML document.
        /// </summary>
        /// <param name="xmlDoc">The XML document to search.</param>
        /// <returns>Returns "Standard" if the invoice type starts with "01", otherwise "Simplified".</returns>
        public static string GetInvoiceType(XmlDocument xmlDoc)
        {
            string typeCode = GetNodeAttributeValue(xmlDoc, XslSettings.Invoice_Type_XPATH, "name");
            if (typeCode.StartsWith("01"))
            {
                return "Standard";
            }
            return "Simplified";
        }

        /// <summary>
        /// Reads an embedded resource stream.
        /// </summary>
        /// <param name="resource">The name of the resource to read.</param>
        /// <returns>Returns a <see cref="Stream"/> containing the resource data.</returns>
        public static Stream ReadInternalEmbededResourceStream(string resource)
        {
            var assemblyName = Assembly.GetExecutingAssembly().ManifestModule.Name;
            resource = assemblyName.Replace(Path.GetExtension(assemblyName), $".{resource}");
            return Assembly.GetExecutingAssembly().GetManifestResourceStream(resource);
        }

        /// <summary>
        /// Writes the specified tag to the stream.
        /// </summary>
        /// <param name="stream">The stream to write the tag to.</param>
        /// <param name="tag">The tag value to write.</param>
        public static void WriteTag(Stream stream, int tag)
        {
            bool firstByte = true;
            for (int i = 3; i >= 0; i--)
            {
                byte thisByte = (byte)(tag >> 8 * i);
                if (!(thisByte == 0 && firstByte) || i <= 0)
                {
                    if (firstByte)
                    {
                        if (i == 0)
                        {
                            if ((thisByte & 0x1F) == 31)
                            {
                                throw new Exception("Invalid tag value: first octet indicates subsequent octets, but no subsequent octets found");
                            }
                        }
                        else if ((thisByte & 0x1F) != 31)
                        {
                            throw new Exception("Invalid tag value: first octet indicates no subsequent octets, but subsequent octets found");
                        }
                    }
                    else if (i == 0)
                    {
                        if ((thisByte & 0x80) == 128)
                        {
                            throw new Exception("Invalid tag value: last octet indicates subsequent octets");
                        }
                    }
                    else if ((thisByte & 0x80) != 128)
                    {
                        throw new Exception("Invalid tag value: non-last octet indicates no subsequent octets");
                    }
                    stream.WriteByte(thisByte);
                    firstByte = false;
                }
            }
        }

        /// <summary>
        /// Writes the specified length to the stream.
        /// </summary>
        /// <param name="stream">The stream to write the length to.</param>
        /// <param name="length">The length value to write, or null for indefinite length.</param>
        public static void WriteLength(Stream stream, int? length)
        {
            if (!length.HasValue)
            {
                stream.WriteByte(128);
                return;
            }
            if (length < 0 || length > uint.MaxValue)
            {
                throw new Exception($"Invalid length value: {length}");
            }
            if (length <= 127)
            {
                stream.WriteByte(checked((byte)length.Value));
                return;
            }
            byte lengthBytes;
            if (length <= 255)
            {
                lengthBytes = 1;
            }
            else if (length <= 65535)
            {
                lengthBytes = 2;
            }
            else if (length <= 16777215)
            {
                lengthBytes = 3;
            }
            else
            {
                if (!(length <= uint.MaxValue))
                {
                    throw new Exception($"Length value too big: {length}");
                }
                lengthBytes = 4;
            }
            stream.WriteByte((byte)(lengthBytes | 0x80u));
            for (int i = lengthBytes - 1; i >= 0; i--)
            {
                byte data = (byte)(length >> 8 * i).Value;
                stream.WriteByte(data);
            }
        }

        /// <summary>
        /// Writes a TLV (Tag-Length-Value) object to a memory stream.
        /// </summary>
        /// <param name="tag">The tag value.</param>
        /// <param name="value">The byte array value.</param>
        /// <returns>Returns a <see cref="MemoryStream"/> containing the TLV object.</returns>
        /// <exception cref="Exception">Throws if the value is null.</exception>
        public static MemoryStream WriteTlv(int tag, byte[] value)
        {
            MemoryStream stream = new MemoryStream();
            WriteTag(stream, tag);
            int length = value != null ? value.Length : 0;
            WriteLength(stream, length);
            if (value == null)
            {
                throw new Exception("Please provide a value!");
            }
            stream.Write(value, 0, length);
            return stream;
        }

    }
}
