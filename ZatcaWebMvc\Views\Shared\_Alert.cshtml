@{
    var type = ViewData["Type"]?.ToString() ?? "info"; // success, danger, warning, info, primary, secondary
    var title = ViewData["Title"]?.ToString();
    var message = ViewData["Message"]?.ToString() ?? "";
    var dismissible = ViewData["Dismissible"]?.ToString() == "true";
    var icon = ViewData["Icon"]?.ToString();
    var showIcon = ViewData["ShowIcon"]?.ToString() != "false";
    
    // Default icons based on type
    if (string.IsNullOrEmpty(icon) && showIcon)
    {
        icon = type switch
        {
            "success" => "bi-check-circle",
            "danger" => "bi-exclamation-triangle",
            "warning" => "bi-exclamation-circle",
            "info" => "bi-info-circle",
            "primary" => "bi-star",
            "secondary" => "bi-gear",
            _ => "bi-info-circle"
        };
    }
}

<div class="alert alert-@type @(dismissible ? "alert-dismissible" : "") fade show" role="alert">
    <div class="d-flex align-items-start">
        @if (showIcon && !string.IsNullOrEmpty(icon))
        {
            <i class="@icon me-2 mt-1 flex-shrink-0"></i>
        }
        <div class="flex-grow-1">
            @if (!string.IsNullOrEmpty(title))
            {
                <h6 class="alert-heading mb-1">@title</h6>
            }
            @if (!string.IsNullOrEmpty(message))
            {
                <div>@Html.Raw(message)</div>
            }
            @if (ViewData.TemplateInfo.HtmlFieldPrefix == null)
            {
                @RenderBody()
            }
        </div>
        @if (dismissible)
        {
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        }
    </div>
</div>

<style>
    .alert {
        border: none;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .alert-heading {
        font-weight: 600;
    }
    
    .alert .bi {
        font-size: 1.1rem;
    }
</style>
