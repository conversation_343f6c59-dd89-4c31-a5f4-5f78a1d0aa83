using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Presentation.Filters
{
    /// <summary>
    /// Operation filter to handle API versioning in Swagger documentation
    /// </summary>
    public class ApiVersionOperationFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            var apiVersionParameter = operation.Parameters?.FirstOrDefault(p => p.Name == "version");
            if (apiVersionParameter != null)
            {
                operation.Parameters.Remove(apiVersionParameter);
            }

            if (operation.Parameters == null)
                operation.Parameters = new List<OpenApiParameter>();

            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "version",
                In = ParameterLocation.Path,
                Required = true,
                Schema = new OpenApiSchema
                {
                    Type = "string",
                    Default = new Microsoft.OpenApi.Any.OpenApiString("1.0")
                },
                Description = "API Version"
            });
        }
    }
}
