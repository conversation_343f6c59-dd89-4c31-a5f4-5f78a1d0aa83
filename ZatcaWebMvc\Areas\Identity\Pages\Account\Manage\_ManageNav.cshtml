@inject SignInManager<ApplicationUser> SignInManager
@{
    var hasExternalLogins = (await SignInManager.GetExternalAuthenticationSchemesAsync()).Any();
}

<div class="modern-card">
    <div class="modern-card-header">
        <h3 class="text-lg font-semibold text-gray-900">Account Settings</h3>
        <p class="text-sm text-gray-600">Manage your account preferences</p>
    </div>

    <nav class="space-y-1">
        <a class="modern-nav-link @(ManageNavPages.IndexNavClass(ViewContext) == "active" ? "modern-nav-link-active" : "")"
           id="profile"
           asp-page="./Index">
            <i class="bi bi-person me-3"></i>
            <span>Profile</span>
        </a>

        <a class="modern-nav-link @(ManageNavPages.ChangePasswordNavClass(ViewContext) == "active" ? "modern-nav-link-active" : "")"
           id="change-password"
           asp-page="./ChangePassword">
            <i class="bi bi-key me-3"></i>
            <span>Password</span>
        </a>

        @if (hasExternalLogins)
        {
            <a class="modern-nav-link @(ManageNavPages.ExternalLoginsNavClass(ViewContext) == "active" ? "modern-nav-link-active" : "")"
               id="external-login"
               asp-page="./ExternalLogins">
                <i class="bi bi-link-45deg me-3"></i>
                <span>External logins</span>
            </a>
        }

        <a class="modern-nav-link @(ManageNavPages.TwoFactorAuthenticationNavClass(ViewContext) == "active" ? "modern-nav-link-active" : "")"
           id="two-factor"
           asp-page="./TwoFactorAuthentication">
            <i class="bi bi-shield-check me-3"></i>
            <span>Two-factor authentication</span>
        </a>

        <a class="modern-nav-link @(ManageNavPages.PersonalDataNavClass(ViewContext) == "active" ? "modern-nav-link-active" : "")"
           id="personal-data"
           asp-page="./PersonalData">
            <i class="bi bi-file-person me-3"></i>
            <span>Personal data</span>
        </a>
    </nav>
</div>
