using ZatcaWebMvc.Models;

namespace ZatcaWebMvc.Services
{
    /// <summary>
    /// Interface for ZATCA API service
    /// </summary>
    public interface IZatcaApiService
    {
        // Invoice operations
        Task<ApiResponse<InvoiceSubmissionResponse>> CreateInvoiceAsync(InvoiceCreateRequest request);
        Task<ApiResponse<InvoiceSubmissionResponse>> GetInvoiceAsync(int id);
        Task<ApiResponse<List<InvoiceSubmissionResponse>>> GetAllInvoicesAsync();

        // Certificate operations
        Task<ApiResponse<CertificateCreationResponse>> CreateCertificateAsync(CertificateCreateRequest request);
        Task<ApiResponse<List<CertificateCreationResponse>>> GetAllCertificatesAsync();
        Task<ApiResponse<CertificateCreationResponse>> GetCertificateAsync(int id);

        // Seller operations
        Task<ApiResponse<SellerResponse>> CreateSellerAsync(SellerCreateRequest request);
        Task<ApiResponse<List<SellerResponse>>> GetAllSellersAsync();
        Task<ApiResponse<SellerResponse>> GetSellerAsync(int id);
        Task<ApiResponse> UpdateSellerAsync(int id, SellerCreateRequest request);
        Task<ApiResponse> DeleteSellerAsync(int id);

        // Health check
        Task<bool> IsApiHealthyAsync();
    }
}
