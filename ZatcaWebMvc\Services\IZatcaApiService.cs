using ZatcaWebMvc.Models;

namespace ZatcaWebMvc.Services
{
    /// <summary>
    /// Enhanced interface for ZATCA API service with comprehensive operations
    /// </summary>
    public interface IZatcaApiService
    {
        #region Invoice Operations
        /// <summary>
        /// Create a new invoice
        /// </summary>
        Task<ApiResponse<InvoiceSubmissionResponse>> CreateInvoiceAsync(InvoiceCreateRequest request);

        /// <summary>
        /// Get invoice by ID
        /// </summary>
        Task<ApiResponse<InvoiceSubmissionResponse>> GetInvoiceAsync(int id);

        /// <summary>
        /// Get all invoices
        /// </summary>
        Task<ApiResponse<List<InvoiceSubmissionResponse>>> GetAllInvoicesAsync();

        /// <summary>
        /// Get invoices with pagination
        /// </summary>
        Task<ApiResponse<List<InvoiceSubmissionResponse>>> GetInvoicesAsync(int page = 1, int pageSize = 10);

        /// <summary>
        /// Submit invoice to ZATCA
        /// </summary>
        Task<ApiResponse<InvoiceSubmissionResponse>> SubmitInvoiceAsync(int invoiceId);
        #endregion

        #region Certificate Operations
        /// <summary>
        /// Create a new certificate
        /// </summary>
        Task<ApiResponse<CertificateCreationResponse>> CreateCertificateAsync(CertificateCreateRequest request);

        /// <summary>
        /// Get all certificates
        /// </summary>
        Task<ApiResponse<List<CertificateCreationResponse>>> GetAllCertificatesAsync();

        /// <summary>
        /// Get certificate by ID
        /// </summary>
        Task<ApiResponse<CertificateCreationResponse>> GetCertificateAsync(int id);

        /// <summary>
        /// Renew certificate
        /// </summary>
        Task<ApiResponse<CertificateCreationResponse>> RenewCertificateAsync(int certificateId);
        #endregion

        #region Seller Operations
        /// <summary>
        /// Create a new seller
        /// </summary>
        Task<ApiResponse<SellerResponse>> CreateSellerAsync(SellerCreateRequest request);

        /// <summary>
        /// Get all sellers
        /// </summary>
        Task<ApiResponse<List<SellerResponse>>> GetAllSellersAsync();

        /// <summary>
        /// Get seller by ID
        /// </summary>
        Task<ApiResponse<SellerResponse>> GetSellerAsync(int id);

        /// <summary>
        /// Update seller information
        /// </summary>
        Task<ApiResponse> UpdateSellerAsync(int id, SellerCreateRequest request);

        /// <summary>
        /// Delete seller
        /// </summary>
        Task<ApiResponse> DeleteSellerAsync(int id);
        #endregion

        #region Health and Diagnostics
        /// <summary>
        /// Check if API is healthy and accessible
        /// </summary>
        Task<bool> IsApiHealthyAsync();

        /// <summary>
        /// Get API connection status with detailed information
        /// </summary>
        Task<ApiResponse<ApiHealthStatus>> GetApiHealthStatusAsync();

        /// <summary>
        /// Test API authentication
        /// </summary>
        Task<ApiResponse> TestAuthenticationAsync();
        #endregion
    }
}
