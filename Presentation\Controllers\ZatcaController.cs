﻿using Application.Contracts.IRepository;
using Application.Contracts.IServices;
using Application.Contracts.Zatca;
using Application.Dtos.Requests;
using Application.Models.Zatca;
using Application.Services;
using Domain.Entities;
using EInvoiceKSADemo.Helpers.Zatca.Helpers;
using Microsoft.AspNetCore.Cors.Infrastructure;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Presentation.Models;
using System.Security.Cryptography.X509Certificates;

namespace Presentation.Controllers
{
    public class ZatcaController : Controller
    {
        private readonly ICertificateCreationService _creationService;
        private readonly IConfiguration _config;
        private readonly IZatcaCSIDIssuer _zatcaCSIDIssuer;
        private readonly ICertificateSettingsRepository _certificateSettingsRepository;
        private readonly IZatcaInvoiceSender _zatcaInvoiceSender;

        public ZatcaController(ICertificateCreationService creationService, IConfiguration config, IZatcaCSIDIssuer zatcaCSIDIssuer, IZatcaInvoiceSender zatcaInvoiceSender, ICertificateSettingsRepository certificateSettingsRepository)
        {
            _creationService = creationService;
            _config = config;
            _zatcaCSIDIssuer= zatcaCSIDIssuer;
            _zatcaInvoiceSender= zatcaInvoiceSender;
            _certificateSettingsRepository = certificateSettingsRepository;
        }

        public IActionResult Index()
        {
            return View();
        }
        public async Task<IActionResult> AddSupplier()
        {
            var result = await _creationService.GetAllSellerIdentitiesAsync();

            var dropdownList = new SelectList(result.Data, "Key", "Description", "CRN");

            ViewData["SellerIdentities"] = dropdownList;

            return View();
        }
        public async Task<IActionResult> CreateCertificate()
        {
            var result = await _creationService.GetAllInvoiceTypesAsync();

            var dropdownList = new SelectList(result.Data, "Key", "Description", "1100");

            ViewData["InvoiceTypes"] = dropdownList;
            return View();
        }
        [HttpPost]
        public async Task<IActionResult> CreateCertificate(ZatcaCsrCreationRequestDto model)
        {
            if (!ModelState.IsValid)

                return BadRequest();
            try
            {
                var result = await _creationService.CreateCertificateAsync(model);
                if (result.Success)
                {
                    return Ok();
                }
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

            return BadRequest();
        }

        [HttpPost]
        public async Task<IActionResult> AddSupplier(ZatcaSupplierCreationRequestDto model)
        {
            if (!ModelState.IsValid)

                return BadRequest();
            try
            {
                var result = await _creationService.AddSupplierAsync(model);
                if (result.Data != null)
                {
                    return Ok();
                }
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
            return BadRequest();
        }
        public async Task<IActionResult> AddOTP()
        {
            string result = _config["ZatcaSettings:Otp"];
            var model = new OTP { Value = result };

            return View(model);
        }
        [HttpPost]
        public async Task<IActionResult> AddOTP(OTP model)
        {
            if (!ModelState.IsValid)

                return BadRequest();
            try
            {
                _config["ZatcaSettings:Otp"]=model.Value;
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

public async Task<IActionResult> ReportInvoice()
        {
            try
            {
                ZatcaInvoiceResultResponse zatcaInvoiceResult= await _zatcaInvoiceSender.SendInvoiceToZatcaAsync();
                //await _zatcaCSIDIssuer.SendInvoiceAsync();
                //ComplianceModelResult complianceModel = new ComplianceModelResult();
                //complianceModel.Secret = "m5tHGy17Vh47XIvnIx9IUcHjUsLdfDIpGZOK881ljzk=";
                //complianceModel.RequestId = 29729193525;
                //complianceModel.BinarySecurityToken = "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";
                //if (complianceModel?.RequestId > 0)
                //{
                //    X509Certificate2 certificate = new X509Certificate2(Convert.FromBase64String(complianceModel.BinarySecurityToken));
                //    if (certificate != null)
                //    {
                //        var result=new CSIDResultModel
                //        {
                //            Secret = complianceModel.Secret,
                //            Certificate = complianceModel.BinarySecurityToken,
                //            StartedDate = certificate.NotBefore,
                //            ExpiredDate = certificate.NotAfter,
                //        };
                //        var certificate1 = new CertificateSettings
                //        {
                //            Certificate = complianceModel.BinarySecurityToken,// "-----BEGIN CERTIFICATE REQUEST-----\r\nMIICGDCCAb0CAQAwezELMAkGA1UEBhMCU0ExCzAJBgNVBAsMAjEwMUIwQAYDVQQK\r\nDDnZhdi12YbYuSDYstiu2KfYsdmBINin2YTYqNmI2YTZiNir2YrYsdmK2YYg2YTZ\r\nhNi12YbYp9i52KkxGzAZBgNVBAMMElpBVENBLUNvZGUtU2lnbmluZzBWMBAGByqG\r\nSM49AgEGBSuBBAAKA0IABDmRiZqtR3mCizZfo87zJfkn6j2S+fPwivD6rj5Aikps\r\nmmZAXOYlWGCRk2vx/Hw3jV5VWukboOQSBiIWz5gDIqSggeIwgd8GCSqGSIb3DQEJ\r\nDjGB0TCBzjAhBgkrBgEEAYI3FAIEFAwSWkFUQ0EtQ29kZS1TaWduaW5nMIGoBgNV\r\nHREEgaAwgZ2kgZowgZcxPjA8BgNVBAQMNTEtUG9zTmFtZXwyLUc0fDMtZjdhNTcw\r\nZjctOGZkYi00ZmJlLWFkNWItMDc2MTVkN2ZiNDFmMR8wHQYKCZImiZPyLGQBAQwP\r\nMzEwNzUyMzMyNTAwMDAzMQ0wCwYDVQQMDAQxMTAwMQ8wDQYDVQQaDAZKZWRkYWgx\r\nFDASBgNVBA8MC0NvbW1lcmljaWFsMAoGCCqGSM49BAMCA0kAMEYCIQDxBFBSZA1c\r\nItD8BHvJhnFXSlAv4V9yoJgdiNY/S4Xx9QIhALfLchQhnI9bLJFdzeGWK8vNIeEG\r\nlvOrfzihdbmmO22Y\r\n-----END CERTIFICATE REQUEST-----",
                //            Secret = result.Secret,
                //            StartedDate = result.StartedDate,
                //            ExpiredDate = result.ExpiredDate,
                //            CertificateDate = DateTime.UtcNow,
                //            Csr = "MIICPjCCAeSgAwIBAgIGAZByyOowMAoGCCqGSM49BAMCMBUxEzARBgNVBAMMCmVJbnZvaWNpbmcwHhcNMjQwNzAyMDkzMTIzWhcNMjkwNzAxMjEwMDAwWjB7MQswCQYDVQQGEwJTQTELMAkGA1UECwwCMTAxQjBABgNVBAoMOdmF2LXZhti5INiy2K7Yp9ix2YEg2KfZhNio2YjZhNmI2KvZitix2YrZhiDZhNmE2LXZhtin2LnYqTEbMBkGA1UEAwwSWkFUQ0EtQ29kZS1TaWduaW5nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEOZGJmq1HeYKLNl+jzvMl+SfqPZL58/CK8PquPkCKSmyaZkBc5iVYYJGTa/H8fDeNXlVa6Rug5BIGIhbPmAMipKOBvDCBuTAMBgNVHRMBAf8EAjAAMIGoBgNVHREEgaAwgZ2kgZowgZcxPjA8BgNVBAQMNTEtUG9zTmFtZXwyLUc0fDMtZjdhNTcwZjctOGZkYi00ZmJlLWFkNWItMDc2MTVkN2ZiNDFmMR8wHQYKCZImiZPyLGQBAQwPMzEwNzUyMzMyNTAwMDAzMQ0wCwYDVQQMDAQxMTAwMQ8wDQYDVQQaDAZKZWRkYWgxFDASBgNVBA8MC0NvbW1lcmljaWFsMAoGCCqGSM49BAMCA0gAMEUCIQCvl796Z8CHD1X1QoId1tMjYciq039VKKCnB4hPaWzFpQIgevT3f8PhwOdrRxbW8bNJM/Skh9pHoVXT6vlfmCTXjek=",
                //            PrivateKey = "-----BEGIN EC PRIVATE KEY-----\r\nMHQCAQEEIPk8AgTrmhuEX6Qngf3wH70n/wk/QO+i9+/73vj0Ii8foAcGBSuBBAAK\r\noUQDQgAEOZGJmq1HeYKLNl+jzvMl+SfqPZL58/CK8PquPkCKSmyaZkBc5iVYYJGT\r\na/H8fDeNXlVa6Rug5BIGIhbPmAMipA==\r\n-----END EC PRIVATE KEY-----",
                //            //UserName = result.Certificate
                //            UserName = result.Certificate
                //        };
                //        await _certificateSettingsRepository.AddAsync(certificate1);

                //    }
                //}
                //return RedirectToAction("Index", "Home");
                return Ok(zatcaInvoiceResult);
            }
            catch (Exception)
            {
                return BadRequest();
            }
        }
    }
}
