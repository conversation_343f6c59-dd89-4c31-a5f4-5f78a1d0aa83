@model string

@if (!String.IsNullOrEmpty(Model))
{
    var statusMessageClass = Model.StartsWith("Error") ? "modern-alert-error" : "modern-alert-success";
    <div class="modern-alert @statusMessageClass mb-6" role="alert">
        <div class="flex">
            @if (Model.StartsWith("Error"))
            {
                <i class="bi bi-exclamation-triangle text-red-500 me-3 mt-0.5"></i>
            }
            else
            {
                <i class="bi bi-check-circle text-green-500 me-3 mt-0.5"></i>
            }
            <div class="flex-1">
                @Model
            </div>
        </div>
    </div>
}
