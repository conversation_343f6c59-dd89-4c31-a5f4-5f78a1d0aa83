﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Persistence.Migrations
{
    public partial class UpdatedInvoiceToZatca : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CustomerId",
                table: "InvoiceToZatcas");

            migrationBuilder.RenameColumn(
                name: "CustomerName",
                table: "InvoiceToZatcas",
                newName: "InvoiceReferenceId");

            migrationBuilder.RenameColumn(
                name: "CustomerAddressDistrict",
                table: "InvoiceToZatcas",
                newName: "InvoicePayments");

            migrationBuilder.RenameColumn(
                name: "CustomerAddressCity",
                table: "InvoiceToZatcas",
                newName: "CustomerJson");

            migrationBuilder.RenameColumn(
                name: "CustomerAddress",
                table: "InvoiceToZatcas",
                newName: "CertificateDetailsId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "InvoiceReferenceId",
                table: "InvoiceToZatcas",
                newName: "CustomerName");

            migrationBuilder.RenameColumn(
                name: "InvoicePayments",
                table: "InvoiceToZatcas",
                newName: "CustomerAddressDistrict");

            migrationBuilder.RenameColumn(
                name: "CustomerJson",
                table: "InvoiceToZatcas",
                newName: "CustomerAddressCity");

            migrationBuilder.RenameColumn(
                name: "CertificateDetailsId",
                table: "InvoiceToZatcas",
                newName: "CustomerAddress");

            migrationBuilder.AddColumn<int>(
                name: "CustomerId",
                table: "InvoiceToZatcas",
                type: "int",
                nullable: true);
        }
    }
}
