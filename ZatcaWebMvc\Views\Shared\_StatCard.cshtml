@{
    var title = ViewData["Title"]?.ToString() ?? "Statistic";
    var value = ViewData["Value"]?.ToString() ?? "0";
    var icon = ViewData["Icon"]?.ToString() ?? "bi-bar-chart";
    var color = ViewData["Color"]?.ToString() ?? "primary";
    var trend = ViewData["Trend"]?.ToString(); // "up", "down", "neutral"
    var trendValue = ViewData["TrendValue"]?.ToString();
    var subtitle = ViewData["Subtitle"]?.ToString();
    var link = ViewData["Link"]?.ToString();
    var linkText = ViewData["LinkText"]?.ToString() ?? "View Details";
}

<div class="card border-0 shadow-sm h-100 stat-card">
    <div class="card-body p-4">
        <div class="d-flex align-items-center justify-content-between mb-3">
            <div class="stat-icon bg-@color bg-opacity-10 rounded-3 p-3">
                <i class="@icon text-@color fs-4"></i>
            </div>
            @if (!string.IsNullOrEmpty(trend) && !string.IsNullOrEmpty(trendValue))
            {
                <div class="trend-indicator">
                    @if (trend == "up")
                    {
                        <span class="badge bg-success bg-opacity-10 text-success">
                            <i class="bi bi-arrow-up me-1"></i>@trendValue
                        </span>
                    }
                    else if (trend == "down")
                    {
                        <span class="badge bg-danger bg-opacity-10 text-danger">
                            <i class="bi bi-arrow-down me-1"></i>@trendValue
                        </span>
                    }
                    else
                    {
                        <span class="badge bg-secondary bg-opacity-10 text-secondary">
                            <i class="bi bi-dash me-1"></i>@trendValue
                        </span>
                    }
                </div>
            }
        </div>
        
        <div class="stat-content">
            <h2 class="stat-value text-@color mb-1">@value</h2>
            <h6 class="stat-title text-muted mb-0">@title</h6>
            @if (!string.IsNullOrEmpty(subtitle))
            {
                <p class="stat-subtitle text-muted small mb-0 mt-1">@subtitle</p>
            }
        </div>
        
        @if (!string.IsNullOrEmpty(link))
        {
            <div class="mt-3 pt-3 border-top">
                <a href="@link" class="btn btn-outline-@color btn-sm">
                    @linkText <i class="bi bi-arrow-right ms-1"></i>
                </a>
            </div>
        }
    </div>
</div>


