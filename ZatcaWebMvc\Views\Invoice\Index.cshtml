@model List<ZatcaWebMvc.Models.InvoiceSubmissionResponse>
@{
    ViewData["Title"] = "Invoice Management";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="bi bi-receipt me-2"></i>Invoice Management
            </h1>
            <p class="text-muted mb-0">Manage and track your ZATCA invoices</p>
        </div>
        <div>
            <a asp-action="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i>Create New Invoice
            </a>
        </div>
    </div>

    <!-- Success Message -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Error Message -->
    @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>@ViewBag.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Invoices Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="bi bi-table me-1"></i>Invoices
            </h6>
        </div>
        <div class="card-body">
            @if (Model != null && Model.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Invoice #</th>
                                <th>Customer</th>
                                <th>Amount</th>
                                <th>VAT</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>ZATCA Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var invoice in Model)
                            {
                                <tr>
                                    <td>
                                        <strong>@invoice.InvoiceNumber</strong>
                                    </td>
                                    <td>@invoice.CustomerName</td>
                                    <td>
                                        <span class="fw-bold text-success">
                                            @invoice.TotalAmount.ToString("C")
                                        </span>
                                    </td>
                                    <td>@invoice.TaxAmount.ToString("C")</td>
                                    <td>@invoice.IssueDate.ToString("dd/MM/yyyy")</td>
                                    <td>
                                        @{
                                            var statusClass = invoice.Status switch
                                            {
                                                "Draft" => "bg-secondary",
                                                "Submitted" => "bg-info",
                                                "Approved" => "bg-success",
                                                "Rejected" => "bg-danger",
                                                _ => "bg-secondary"
                                            };
                                        }
                                        <span class="badge @statusClass">@invoice.Status</span>
                                    </td>
                                    <td>
                                        @if (invoice.ReportedToZatca)
                                        {
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>Reported
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-warning">
                                                <i class="bi bi-clock me-1"></i>Pending
                                            </span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a asp-action="Details" asp-route-id="@invoice.Id" 
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            @if (!invoice.ReportedToZatca)
                                            {
                                                <form asp-action="Submit" method="post" style="display: inline;">
                                                    <input type="hidden" name="id" value="@invoice.Id" />
                                                    <button type="submit" class="btn btn-sm btn-outline-success" 
                                                            title="Submit to ZATCA"
                                                            onclick="return confirm('Are you sure you want to submit this invoice to ZATCA?')">
                                                        <i class="bi bi-send"></i>
                                                    </button>
                                                </form>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-receipt display-1 text-muted"></i>
                    <h4 class="mt-3 text-muted">No invoices found</h4>
                    <p class="text-muted">Create your first invoice to get started.</p>
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>Create Invoice
                    </a>
                </div>
            }
        </div>
    </div>
</div>
