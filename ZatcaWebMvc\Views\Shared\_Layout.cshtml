﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - ZATCA E-Invoice Management</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/ZatcaWebMvc.styles.css" asp-append-version="true" />
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Top Navigation -->
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm fixed-top">
            <div class="container-fluid">
                <!-- Brand -->
                <a class="navbar-brand fw-bold d-flex align-items-center" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-receipt me-2 fs-4"></i>
                    <span class="d-none d-md-inline">ZATCA E-Invoice</span>
                    <span class="d-md-none">ZATCA</span>
                </a>

                <!-- Mobile Toggle -->
                <button class="navbar-toggler border-0" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebarOffcanvas" aria-controls="sidebarOffcanvas">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Desktop Navigation -->
                <div class="collapse navbar-collapse d-none d-lg-flex" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="bi bi-speedometer2 me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-file-earmark-text me-1"></i>Invoices
                            </a>
                            <ul class="dropdown-menu shadow border-0">
                                <li><a class="dropdown-item" asp-controller="Invoice" asp-action="Index">
                                    <i class="bi bi-list me-2 text-primary"></i>View All Invoices
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Invoice" asp-action="Create">
                                    <i class="bi bi-plus-circle me-2 text-success"></i>Create New Invoice
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" asp-controller="Invoice" asp-action="Pending">
                                    <i class="bi bi-clock me-2 text-warning"></i>Pending Invoices
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-shield-check me-1"></i>Certificates
                            </a>
                            <ul class="dropdown-menu shadow border-0">
                                <li><a class="dropdown-item" asp-controller="Certificate" asp-action="Index">
                                    <i class="bi bi-list me-2 text-primary"></i>View All Certificates
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Certificate" asp-action="Create">
                                    <i class="bi bi-plus-circle me-2 text-success"></i>Create New Certificate
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" asp-controller="Certificate" asp-action="Expired">
                                    <i class="bi bi-exclamation-triangle me-2 text-warning"></i>Expired Certificates
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-building me-1"></i>Sellers
                            </a>
                            <ul class="dropdown-menu shadow border-0">
                                <li><a class="dropdown-item" asp-controller="Seller" asp-action="Index">
                                    <i class="bi bi-list me-2 text-primary"></i>View All Sellers
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Seller" asp-action="Create">
                                    <i class="bi bi-plus-circle me-2 text-success"></i>Create New Seller
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="ApiTest" asp-action="Index">
                                <i class="bi bi-gear me-1"></i>API Test
                            </a>
                        </li>
                    </ul>

                    <!-- User Authentication Menu -->
                    <ul class="navbar-nav">
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="user-avatar bg-light rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                        <i class="bi bi-person text-primary"></i>
                                    </div>
                                    <span class="d-none d-xl-inline">@User.Identity.Name</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end shadow border-0">
                                    <li><h6 class="dropdown-header">
                                        <i class="bi bi-person-circle me-1"></i>@User.Identity.Name
                                    </h6></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Index">
                                        <i class="bi bi-gear me-2 text-primary"></i>Profile Settings
                                    </a></li>
                                    <li><a class="dropdown-item" href="#">
                                        <i class="bi bi-bell me-2 text-info"></i>Notifications
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home")" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="bi bi-box-arrow-right me-2"></i>Logout
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link btn btn-outline-light btn-sm me-2" asp-area="Identity" asp-page="/Account/Login">
                                    <i class="bi bi-box-arrow-in-right me-1"></i>Login
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link btn btn-light btn-sm text-primary" asp-area="Identity" asp-page="/Account/Register">
                                    <i class="bi bi-person-plus me-1"></i>Register
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Mobile Sidebar Offcanvas -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="sidebarOffcanvas" aria-labelledby="sidebarOffcanvasLabel">
        <div class="offcanvas-header bg-primary text-white">
            <h5 class="offcanvas-title" id="sidebarOffcanvasLabel">
                <i class="bi bi-receipt me-2"></i>ZATCA E-Invoice
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body p-0">
            <nav class="nav flex-column">
                <a class="nav-link" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-speedometer2 me-2"></i>Dashboard
                </a>

                <div class="nav-section">
                    <h6 class="nav-section-title">Invoices</h6>
                    <a class="nav-link" asp-controller="Invoice" asp-action="Index">
                        <i class="bi bi-list me-2"></i>View All Invoices
                    </a>
                    <a class="nav-link" asp-controller="Invoice" asp-action="Create">
                        <i class="bi bi-plus-circle me-2"></i>Create New Invoice
                    </a>
                    <a class="nav-link" asp-controller="Invoice" asp-action="Pending">
                        <i class="bi bi-clock me-2"></i>Pending Invoices
                    </a>
                </div>

                <div class="nav-section">
                    <h6 class="nav-section-title">Certificates</h6>
                    <a class="nav-link" asp-controller="Certificate" asp-action="Index">
                        <i class="bi bi-list me-2"></i>View All Certificates
                    </a>
                    <a class="nav-link" asp-controller="Certificate" asp-action="Create">
                        <i class="bi bi-plus-circle me-2"></i>Create New Certificate
                    </a>
                    <a class="nav-link" asp-controller="Certificate" asp-action="Expired">
                        <i class="bi bi-exclamation-triangle me-2"></i>Expired Certificates
                    </a>
                </div>

                <div class="nav-section">
                    <h6 class="nav-section-title">Sellers</h6>
                    <a class="nav-link" asp-controller="Seller" asp-action="Index">
                        <i class="bi bi-list me-2"></i>View All Sellers
                    </a>
                    <a class="nav-link" asp-controller="Seller" asp-action="Create">
                        <i class="bi bi-plus-circle me-2"></i>Create New Seller
                    </a>
                </div>

                <div class="nav-section">
                    <h6 class="nav-section-title">System</h6>
                    <a class="nav-link" asp-controller="ApiTest" asp-action="Index">
                        <i class="bi bi-gear me-2"></i>API Test
                    </a>
                </div>
            </nav>
        </div>
    </div>

    <!-- Main Content with Top Padding for Fixed Navbar -->
    <main class="flex-grow-1" style="padding-top: 76px;">
        <!-- Toast Container for Notifications -->
        <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
            <div id="liveToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="bi bi-bell-fill me-2 text-primary"></i>
                    <strong class="me-auto">Notification</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body"></div>
            </div>
        </div>

        <!-- Alert Messages -->
        <div class="container-fluid">
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show shadow-sm border-0 mt-3" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-check-circle-fill me-2 fs-5"></i>
                        <div>@TempData["SuccessMessage"]</div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show shadow-sm border-0 mt-3" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-exclamation-triangle-fill me-2 fs-5"></i>
                        <div>@TempData["ErrorMessage"]</div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            @if (TempData["InfoMessage"] != null)
            {
                <div class="alert alert-info alert-dismissible fade show shadow-sm border-0 mt-3" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-info-circle-fill me-2 fs-5"></i>
                        <div>@TempData["InfoMessage"]</div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
        </div>

        <!-- Page Content -->
        <div class="container-fluid py-4">
            @RenderBody()
        </div>
    </main>

    <footer class="bg-light border-top mt-auto">
        <div class="container py-3">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        &copy; @DateTime.Now.Year - ZATCA E-Invoice Management System
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="text-muted mb-0">
                        <i class="bi bi-envelope me-1"></i>
                        <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery (if needed for legacy code) -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <!-- Custom JS -->
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
