﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - ZATCA E-Invoice Management</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/ZatcaWebMvc.styles.css" asp-append-version="true" />
</head>
<body class="d-flex flex-column min-vh-100">
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
            <div class="container">
                <a class="navbar-brand fw-bold" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-receipt me-2"></i>ZATCA E-Invoice
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                        aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="bi bi-house me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-file-earmark-text me-1"></i>Invoices
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" asp-controller="Invoice" asp-action="Index">
                                    <i class="bi bi-list me-1"></i>View All
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Invoice" asp-action="Create">
                                    <i class="bi bi-plus-circle me-1"></i>Create New
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-shield-check me-1"></i>Certificates
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" asp-controller="Certificate" asp-action="Index">
                                    <i class="bi bi-list me-1"></i>View All
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Certificate" asp-action="Create">
                                    <i class="bi bi-plus-circle me-1"></i>Create New
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-building me-1"></i>Sellers
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" asp-controller="Seller" asp-action="Index">
                                    <i class="bi bi-list me-1"></i>View All
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Seller" asp-action="Create">
                                    <i class="bi bi-plus-circle me-1"></i>Create New
                                </a></li>
                            </ul>
                        </li>
                    </ul>

                    <!-- User Authentication Menu -->
                    <ul class="navbar-nav">
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-person-circle me-1"></i>@User.Identity.Name
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Index">
                                        <i class="bi bi-gear me-1"></i>Profile
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home")" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-box-arrow-right me-1"></i>Logout
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-area="Identity" asp-page="/Account/Login">
                                    <i class="bi bi-box-arrow-in-right me-1"></i>Login
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-area="Identity" asp-page="/Account/Register">
                                    <i class="bi bi-person-plus me-1"></i>Register
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="flex-grow-1">
        <div class="container-fluid py-4">
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>@TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>@TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            @if (TempData["InfoMessage"] != null)
            {
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="bi bi-info-circle me-2"></i>@TempData["InfoMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            @RenderBody()
        </div>
    </main>

    <footer class="bg-light border-top mt-auto">
        <div class="container py-3">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        &copy; @DateTime.Now.Year - ZATCA E-Invoice Management System
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="text-muted mb-0">
                        <i class="bi bi-envelope me-1"></i>
                        <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery (if needed for legacy code) -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <!-- Custom JS -->
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
