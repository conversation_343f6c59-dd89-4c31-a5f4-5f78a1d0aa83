﻿using Application.Contracts.IRepository;
using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Persistence.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Persistence.Repositories
{
    public class InvoiceToZatcaRepository : IInvoiceToZatcaRepository
    {
        private readonly ApplicationDbContext _context;
        public InvoiceToZatcaRepository(ApplicationDbContext context)
        {
            _context = context;
        }
        /// <summary>
        /// Add list of invoices to DB that will be sent
        /// </summary>
        /// <param name="invoices">List of invoices</param>
        public async Task AddRangeInvoicesAsync(List<InvoiceToZatca> invoices)
        {
            await _context.EI_InvoiceToZatcas.AddRangeAsync(invoices);
            await _context.SaveChangesAsync();
        }
        /// <summary>
        /// Getting all invoices that stored in DB is ready to send to zatca
        /// </summary>
        /// <returns>Ready to send invoice list</returns>
        public async Task<IReadOnlyList<InvoiceToZatca>> GetAllInvoicesToSendAsync()
        {
            return await _context.EI_InvoiceToZatcas
                .Where(i => (i.IsSent == false) || (i.IsSent == true && i.IsAccepted == false && i.CountOfRetries < 3))
                .ToListAsync();
        }
        /// <summary>
        /// updating spicified invoice info or details info 
        /// </summary>
        /// <param name="invoice">Invoice info and details info of the invoice are embedded</param>
        public async Task UpdateInvoiceAsync(InvoiceToZatca invoice)
        {
            _context.EI_InvoiceToZatcas.Update(invoice);
            await _context.SaveChangesAsync();
        }
        /// <summary>
        /// Update list of invoices async
        /// </summary>
        /// <param name="invoices">List of invoices that need to be updated</param>
        public async Task UpdateInvoicesAsync(List<InvoiceToZatca> invoices)
        {
            _context.EI_InvoiceToZatcas.UpdateRange(invoices);
            await _context.SaveChangesAsync();
        }
    }
}
