﻿using Application.Contracts.IRepository;
using Application.Contracts.IServices;
using Application.Contracts.Zatca;
using Application.Models.Zatca;
using Domain.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EInvoiceKSADemo.Helpers.Zatca.Helpers
{
    public class CertificateConfiguration : ICertificateConfiguration
    {
        private readonly ICertificateSettingsRepository _certificateSettingsRepository;

        public CertificateConfiguration(ICertificateSettingsRepository certificateSettingsRepository)
        {
            _certificateSettingsRepository = certificateSettingsRepository;
        }

        public CertificateSettings GetCertificateDetails()
        {
            //var certificateDetails = new CertificateDetails();

            CertificateSettings certificate = _certificateSettingsRepository.GetCertificateSettings();

            if(certificate is not null)
            {
                certificate.UserName = certificate.Certificate;
                // File & Cloud & Database 
                //certificateDetails = new CertificateDetails
                //{
                //    Certificate = certificate.Certificate,
                //    PrivateKey = certificate.PrivateKey,
                //    CSR = certificate.Csr,
                //    ExpiredDate = (DateTime)certificate.ExpiredDate,
                //    StartedDate = (DateTime)certificate.StartedDate,
                //    Secret = certificate.Secret,
                //    UserName = certificate.Certificate
                //};
                return certificate;
            }
            else
            {
                //Test Data
                certificate = new CertificateSettings ()
                {
                    Id=Guid.NewGuid(),
                    Certificate = "TUlJRXREQ0NCRnFnQXdJQkFnSVRZd0FBS0RwNGtpVXRidHd6bVFBQkFBQW9PakFLQmdncWhrak9QUVFEQWpCaU1SVXdFd1lLQ1pJbWlaUHlMR1FCR1JZRmJHOWpZV3d4RXpBUkJnb0praWFKay9Jc1pBRVpGZ05uYjNZeEZ6QVZCZ29Ka2lhSmsvSXNaQUVaRmdkbGVIUm5ZWHAwTVJzd0dRWURWUVFERXhKUVJWcEZTVTVXVDBsRFJWTkRRVEV0UTBFd0hoY05NalF3TmpJM01UTTBNekl5V2hjTk1qWXdOakkzTVRNMU16SXlXakNCcERFTE1Ba0dBMVVFQmhNQ1UwRXhRakJBQmdOVkJBb01PZG1GMkxYWmh0aTVJTml5Mks3WXA5aXgyWUVnMktmWmhOaW8yWWpaaE5tSTJLdlppdGl4MllyWmhpRFpoTm1FMkxYWmh0aW4yTG5ZcVRFVE1CRUdBMVVFQ3hNS016RXdOelV5TXpNeU5URVlNQllHQTFVRUF4TVBNekV3TnpVeU16TXlOVEF3TURBek1TSXdJQVlKS29aSWh2Y05BUWtCRmhOaExtRnNhVUJzYm1scmFYUnplWE11WTI5dE1GWXdFQVlIS29aSXpqMENBUVlGSzRFRUFBb0RRZ0FFTnlBUVpTSHdIVjB6RWRQRDRGVGNoei8xVkM2WjF6b0ppVWxMWUd4cld6dU5NMlM1aVRha0VTdEpFakl6SWJiKzlwR2xLb05qMGxpZmtzK3JPSHovU0tPQ0FxMHdnZ0twTUlIL0JnTlZIUkVFZ2Zjd2dmU2tnZkV3Z2U0eGNEQnVCZ05WQkFRTVp6RXQyWVhZdGRtRzJMa2cyTExZcnRpbjJMSFpnU0RZcDltRTJLalppTm1FMllqWXE5bUsyTEhaaXRtR0lObUUyWVRZdGRtRzJLZll1ZGlwZkRJdFZqRjhNeTFoTUdOaE1HVTVNeTAwTWpreUxUUTBOamN0T1dNelppMDNaREV6TmpkbVlUTTNOMkl4SHpBZEJnb0praWFKay9Jc1pBRUJEQTh6TVRBM05USXpNekkxTURBd01ETXhEVEFMQmdOVkJBd01CREV3TURBeE5EQXlCZ05WQkJvTUs5aXMySy9ZcVNBdElObUYySy9aaXRtRzJLa2cyS2ZaaE5pbzJZYllwOWloSUMwZzJZWFl1ZGl4MkxZeEZEQVNCZ05WQkE4TUMwTnZiVzFsY21samFXRnNNQjBHQTFVZERnUVdCQlNnRGhNVzRSM1hwemRvWHIyYytud1Q2WFNuNERBZkJnTlZIU01FR0RBV2dCU3FXRGlEcVpZbFRlcFVlcm1VQitOWFVQZzc2RENCemdZSUt3WUJCUVVIQVFFRWdjRXdnYjR3Z2JzR0NDc0dBUVVGQnpBQ2hvR3ViR1JoY0Rvdkx5OURUajFRUlZwRlNVNVdUMGxEUlZORFFURXRRMEVzUTA0OVFVbEJMRU5PUFZCMVlteHBZeVV5TUV0bGVTVXlNRk5sY25acFkyVnpMRU5PUFZObGNuWnBZMlZ6TEVOT1BVTnZibVpwWjNWeVlYUnBiMjRzUkVNOVpYaDBlbUYwWTJFc1JFTTlaMjkyTEVSRFBXeHZZMkZzUDJOQlEyVnlkR2xtYVdOaGRHVS9ZbUZ6WlQ5dlltcGxZM1JEYkdGemN6MWpaWEowYVdacFkyRjBhVzl1UVhWMGFHOXlhWFI1TUE0R0ExVWREd0VCL3dRRUF3SUhnREE4QmdrckJnRUVBWUkzRlFjRUx6QXRCaVVyQmdFRUFZSTNGUWlCaHFnZGhORDdFb2J0blNTSHp2c1owOEJWWm9HYzJDMkQ1Y1ZkQWdGa0FnRVFNQjBHQTFVZEpRUVdNQlFHQ0NzR0FRVUZCd01DQmdnckJnRUZCUWNEQXpBbkJna3JCZ0VFQVlJM0ZRb0VHakFZTUFvR0NDc0dBUVVGQndNQ01Bb0dDQ3NHQVFVRkJ3TURNQW9HQ0NxR1NNNDlCQU1DQTBnQU1FVUNJRit0QVJ0UnFJbWNldlQvTE5XVWZkUXpaaGFoZVh2WHhUWlZOMXNvYk9Ka0FpRUFnOTR0MUc1K0drczZDZ21tSXBSU3FoN3c4azhtWWxXaW5DNlE2MlZmelFFPQ==",
                    PrivateKey = "-----BEGIN EC PRIVATE KEY-----\r\nMHQCAQEEINUF6m41uuD559uGnS8xVe+TZqc/5+d9FQhv7A08ZM/noAcGBSuBBAAK\r\noUQDQgAENyAQZSHwHV0zEdPD4FTchz/1VC6Z1zoJiUlLYGxrWzuNM2S5iTakEStJ\r\nEjIzIbb+9pGlKoNj0lifks+rOHz/SA==\r\n-----END EC PRIVATE KEY-----\r\n",
                    Csr = "-----BEGIN CERTIFICATE REQUEST-----\r\nMIICnjCCAkUCAQAwgaQxIjAgBgkqhkiG9w0BCQEWE2EuYWxpQGxuaWtpdHN5cy5j\r\nb20xCzAJBgNVBAYTAlNBMRMwEQYDVQQLDAozMTA3NTIzMzI1MUIwQAYDVQQKDDnZ\r\nhdi12YbYuSDYstiu2KfYsdmBINin2YTYqNmI2YTZiNir2YrYsdmK2YYg2YTZhNi1\r\n2YbYp9i52KkxGDAWBgNVBAMMDzMxMDc1MjMzMjUwMDAwMzBWMBAGByqGSM49AgEG\r\nBSuBBAAKA0IABDcgEGUh8B1dMxHTw+BU3Ic/9VQumdc6CYlJS2Bsa1s7jTNkuYk2\r\npBErSRIyMyG2/vaRpSqDY9JYn5LPqzh8/0igggE/MIIBOwYJKoZIhvcNAQkOMYIB\r\nLDCCASgwJAYJKwYBBAGCNxQCBBcTFVBSRVpBVENBLUNvZGUtU2lnbmluZzCB/wYD\r\nVR0RBIH3MIH0pIHxMIHuMXAwbgYDVQQEDGcxLdmF2LXZhti5INiy2K7Yp9ix2YEg\r\n2KfZhNio2YjZhNmI2KvZitix2YrZhiDZhNmE2LXZhtin2LnYqXwyLVYxfDMtYTBj\r\nYTBlOTMtNDI5Mi00NDY3LTljM2YtN2QxMzY3ZmEzNzdiMR8wHQYKCZImiZPyLGQB\r\nAQwPMzEwNzUyMzMyNTAwMDAzMQ0wCwYDVQQMDAQxMDAwMTQwMgYDVQQaDCvYrNiv\r\n2KkgLSDZhdiv2YrZhtipINin2YTYqNmG2KfYoSAtINmF2LnYsdi2MRQwEgYDVQQP\r\nDAtDb21tZXJpY2lhbDAKBggqhkjOPQQDAgNHADBEAiBeQgvtnQaT50GbJoP32Jiz\r\nA/CrlGVHcdfTYX4gZa8S5wIgDU4PQPwXnlJ/cCfx51RrlDhWxam24jVbeBIfwSS+\r\nFZU=\r\n-----END CERTIFICATE REQUEST-----\r\n",
                    ExpiredDate = DateTime.Now.AddYears(5),
                    StartedDate = DateTime.Now,
                    Secret = "TAWYkAhdi7/V0sVNKcMCZ0r/77QbaDsjjMpqegtuLqc=",
                    UserName = "TUlJRXREQ0NCRnFnQXdJQkFnSVRZd0FBS0RwNGtpVXRidHd6bVFBQkFBQW9PakFLQmdncWhrak9QUVFEQWpCaU1SVXdFd1lLQ1pJbWlaUHlMR1FCR1JZRmJHOWpZV3d4RXpBUkJnb0praWFKay9Jc1pBRVpGZ05uYjNZeEZ6QVZCZ29Ka2lhSmsvSXNaQUVaRmdkbGVIUm5ZWHAwTVJzd0dRWURWUVFERXhKUVJWcEZTVTVXVDBsRFJWTkRRVEV0UTBFd0hoY05NalF3TmpJM01UTTBNekl5V2hjTk1qWXdOakkzTVRNMU16SXlXakNCcERFTE1Ba0dBMVVFQmhNQ1UwRXhRakJBQmdOVkJBb01PZG1GMkxYWmh0aTVJTml5Mks3WXA5aXgyWUVnMktmWmhOaW8yWWpaaE5tSTJLdlppdGl4MllyWmhpRFpoTm1FMkxYWmh0aW4yTG5ZcVRFVE1CRUdBMVVFQ3hNS016RXdOelV5TXpNeU5URVlNQllHQTFVRUF4TVBNekV3TnpVeU16TXlOVEF3TURBek1TSXdJQVlKS29aSWh2Y05BUWtCRmhOaExtRnNhVUJzYm1scmFYUnplWE11WTI5dE1GWXdFQVlIS29aSXpqMENBUVlGSzRFRUFBb0RRZ0FFTnlBUVpTSHdIVjB6RWRQRDRGVGNoei8xVkM2WjF6b0ppVWxMWUd4cld6dU5NMlM1aVRha0VTdEpFakl6SWJiKzlwR2xLb05qMGxpZmtzK3JPSHovU0tPQ0FxMHdnZ0twTUlIL0JnTlZIUkVFZ2Zjd2dmU2tnZkV3Z2U0eGNEQnVCZ05WQkFRTVp6RXQyWVhZdGRtRzJMa2cyTExZcnRpbjJMSFpnU0RZcDltRTJLalppTm1FMllqWXE5bUsyTEhaaXRtR0lObUUyWVRZdGRtRzJLZll1ZGlwZkRJdFZqRjhNeTFoTUdOaE1HVTVNeTAwTWpreUxUUTBOamN0T1dNelppMDNaREV6TmpkbVlUTTNOMkl4SHpBZEJnb0praWFKay9Jc1pBRUJEQTh6TVRBM05USXpNekkxTURBd01ETXhEVEFMQmdOVkJBd01CREV3TURBeE5EQXlCZ05WQkJvTUs5aXMySy9ZcVNBdElObUYySy9aaXRtRzJLa2cyS2ZaaE5pbzJZYllwOWloSUMwZzJZWFl1ZGl4MkxZeEZEQVNCZ05WQkE4TUMwTnZiVzFsY21samFXRnNNQjBHQTFVZERnUVdCQlNnRGhNVzRSM1hwemRvWHIyYytud1Q2WFNuNERBZkJnTlZIU01FR0RBV2dCU3FXRGlEcVpZbFRlcFVlcm1VQitOWFVQZzc2RENCemdZSUt3WUJCUVVIQVFFRWdjRXdnYjR3Z2JzR0NDc0dBUVVGQnpBQ2hvR3ViR1JoY0Rvdkx5OURUajFRUlZwRlNVNVdUMGxEUlZORFFURXRRMEVzUTA0OVFVbEJMRU5PUFZCMVlteHBZeVV5TUV0bGVTVXlNRk5sY25acFkyVnpMRU5PUFZObGNuWnBZMlZ6TEVOT1BVTnZibVpwWjNWeVlYUnBiMjRzUkVNOVpYaDBlbUYwWTJFc1JFTTlaMjkyTEVSRFBXeHZZMkZzUDJOQlEyVnlkR2xtYVdOaGRHVS9ZbUZ6WlQ5dlltcGxZM1JEYkdGemN6MWpaWEowYVdacFkyRjBhVzl1UVhWMGFHOXlhWFI1TUE0R0ExVWREd0VCL3dRRUF3SUhnREE4QmdrckJnRUVBWUkzRlFjRUx6QXRCaVVyQmdFRUFZSTNGUWlCaHFnZGhORDdFb2J0blNTSHp2c1owOEJWWm9HYzJDMkQ1Y1ZkQWdGa0FnRVFNQjBHQTFVZEpRUVdNQlFHQ0NzR0FRVUZCd01DQmdnckJnRUZCUWNEQXpBbkJna3JCZ0VFQVlJM0ZRb0VHakFZTUFvR0NDc0dBUVVGQndNQ01Bb0dDQ3NHQVFVRkJ3TURNQW9HQ0NxR1NNNDlCQU1DQTBnQU1FVUNJRit0QVJ0UnFJbWNldlQvTE5XVWZkUXpaaGFoZVh2WHhUWlZOMXNvYk9Ka0FpRUFnOTR0MUc1K0drczZDZ21tSXBSU3FoN3c4azhtWWxXaW5DNlE2MlZmelFFPQ==",
                    CertificateDate= DateTime.Now,
                };
            }

            return certificate;
        }

        public async Task<CertificateSettings> GetCertificateDetails(string companyId)
        {
            // select * from CertificateSettings where companyId == companyId;
            var certificate =await _certificateSettingsRepository.GetCertificateByIdAsync(companyId);
            if(certificate!=null) certificate.UserName = certificate?.Certificate;
            return certificate;
        }
        public async Task<IEnumerable<CertificateSettings>> GetCertificatesDetails()
        {
           return await _certificateSettingsRepository.GetCertificatesSettings();
        }
        public async Task SaveCsrAndPK(InputCsrModel model)
        {
            var cert = new CertificateDetails
            {
                CSR = model.CSR,
                PrivateKey = model.PrivateKey,
            };

            //Save Cert to Database


        }

        public async Task UpdateCertificate(CSIDResultModel model)
        {
            var cert = GetCertificateDetails(); //model.CompanyId;
            if (cert != null)
            {
                cert.UserName = model.Certificate;
                cert.Certificate = model.Certificate;
                cert.Secret = model.Secret;
                cert.ExpiredDate = model.ExpiredDate;
                cert.StartedDate = model.StartedDate;
            }

            // Update Certificate Details in Database
        }
        public async Task RemoveCertificate(CertificateSettings certificateSettings)
        {
            await _certificateSettingsRepository.RemoveAsync(certificateSettings);
           
        }
    }
}
