﻿using Application.Contracts.IRepository;
using Domain.Entities;
using Microsoft.AspNetCore.Mvc;
using NuGet.Protocol;
using Persistence.Repositories;
using Presentation.Models;
using System.ComponentModel.DataAnnotations;


namespace Presentation.Controllers
{
    /// <summary>
    /// Controller for managing seller/supplier information
    /// </summary>
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    [ApiVersion("1.0")]
    [Produces("application/json")]
    [Tags("Seller Management")]
    public class SellerController : ControllerBase
    {
        private readonly ISupplierRepository _supplierRepository;
        private readonly ILogger<SellerController> _logger;

        /// <summary>
        /// Initializes a new instance of the SellerController
        /// </summary>
        /// <param name="supplierRepository">Repository for supplier operations</param>
        /// <param name="logger">Logger instance</param>
        public SellerController(
            ISupplierRepository supplierRepository,
            ILogger<SellerController> logger)
        {
            _supplierRepository = supplierRepository;
            _logger = logger;
        }
        /// <summary>
        /// Retrieves the current seller/supplier information
        /// </summary>
        /// <returns>Seller information</returns>
        /// <response code="200">Seller information retrieved successfully</response>
        /// <response code="404">No seller found</response>
        /// <response code="500">Internal server error</response>
        [HttpGet]
        [ProducesResponseType(typeof(ApiResponse<SellerResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetSeller()
        {
            try
            {
                _logger.LogInformation("Retrieving seller information");

                var seller = await _supplierRepository.GetSupplierAsync();

                if (seller == null)
                {
                    _logger.LogInformation("No seller found in the system");
                    return NotFound(ApiResponse.ErrorResponse(
                        "No seller information found",
                        404));
                }
                else
                {
                    _logger.LogInformation("Seller information retrieved successfully");
                    var sellerResponse = MapToSellerResponse(seller);
                    return Ok(ApiResponse<SellerResponse>.SuccessResponse(
                        sellerResponse,
                        "Seller information retrieved successfully"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving seller information");
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "An error occurred while retrieving seller information",
                    500,
                    new List<string> { ex.Message }));
            }
        }

        /// <summary>
        /// Creates a new seller/supplier in the system
        /// </summary>
        /// <param name="sellerRequest">Seller information to create</param>
        /// <returns>Created seller information</returns>
        /// <response code="201">Seller created successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="409">Seller already exists</response>
        /// <response code="500">Internal server error</response>
        [HttpPost]
        [ProducesResponseType(typeof(ApiResponse<SellerResponse>), 201)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 409)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> CreateSeller([FromBody] SellerRequest sellerRequest)
        {
            if (sellerRequest == null)
            {
                _logger.LogWarning("Seller creation failed: Null seller data received");
                return BadRequest(ApiResponse.ErrorResponse(
                    "Seller data is required",
                    400));
            }

            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();

                _logger.LogWarning("Seller creation failed: Validation errors - {Errors}", string.Join(", ", errors));
                return BadRequest(ApiResponse.ValidationErrorResponse(errors));
            }

            try
            {
                _logger.LogInformation("Creating seller: {OrganizationName}", sellerRequest.OrganizationName);

                if (await _supplierRepository.IsSupplierFoundAsync())
                {
                    _logger.LogWarning("Seller creation failed: Seller already exists");
                    return Conflict(ApiResponse.ErrorResponse(
                        "A seller already exists in the system. Only one seller is allowed.",
                        409));
                }

                var seller = MapToSellerEntity(sellerRequest);
                await _supplierRepository.AddSupplierAsync(seller);

                _logger.LogInformation("Seller created successfully: {OrganizationName}", sellerRequest.OrganizationName);

                var sellerResponse = MapToSellerResponse(seller);
                return CreatedAtAction(
                    nameof(GetSeller),
                    ApiResponse<SellerResponse>.SuccessResponse(
                        sellerResponse,
                        "Seller created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating seller: {OrganizationName}", sellerRequest.OrganizationName);
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "An error occurred while creating the seller",
                    500,
                    new List<string> { ex.Message }));
            }
        }

        /// <summary>
        /// Deletes the current seller/supplier from the system
        /// </summary>
        /// <returns>Deletion result</returns>
        /// <response code="200">Seller deleted successfully</response>
        /// <response code="404">No seller found to delete</response>
        /// <response code="500">Internal server error</response>
        [HttpDelete]
        [ProducesResponseType(typeof(ApiResponse), 200)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> DeleteSeller()
        {
            try
            {
                _logger.LogInformation("Deleting seller");

                var seller = await _supplierRepository.GetSupplierAsync();

                if (seller != null)
                {
                    await _supplierRepository.DeleteSupplierAsync(seller);

                    _logger.LogInformation("Seller deleted successfully: {SellerName}", seller.SellerName);
                    return Ok(ApiResponse.SuccessResponse("Seller deleted successfully"));
                }
                else
                {
                    _logger.LogWarning("Delete seller failed: No seller found");
                    return NotFound(ApiResponse.ErrorResponse(
                        "No seller found to delete",
                        404));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting seller");
                return StatusCode(500, ApiResponse.ErrorResponse(
                    "An error occurred while deleting the seller",
                    500,
                    new List<string> { ex.Message }));
            }
        }

        /// <summary>
        /// Maps seller entity to response model
        /// </summary>
        private static SellerResponse MapToSellerResponse(Seller seller)
        {
            return new SellerResponse
            {
                Id = (int)seller.Id.GetHashCode(), // Convert Guid to int for response
                OrganizationName = seller.SellerName ?? string.Empty,
                VatNumber = seller.SellerTRN ?? string.Empty,
                Address = seller.StreetName ?? string.Empty,
                City = seller.CityName ?? string.Empty,
                District = seller.DistrictName ?? string.Empty,
                CountryCode = seller.CountryCode ?? "SA",
                Email = string.Empty, // Not available in Seller entity
                Phone = string.Empty, // Not available in Seller entity
                BusinessCategory = string.Empty, // Not available in Seller entity
                CreatedDate = DateTime.UtcNow, // Not available in Seller entity
                ModifiedDate = DateTime.UtcNow // Not available in Seller entity
            };
        }

        /// <summary>
        /// Maps seller request to entity
        /// </summary>
        private static Seller MapToSellerEntity(SellerRequest sellerRequest)
        {
            return new Seller
            {
                Id = Guid.NewGuid(),
                SellerName = sellerRequest.OrganizationName,
                SellerTRN = sellerRequest.VatNumber,
                StreetName = sellerRequest.Address,
                CityName = sellerRequest.City,
                DistrictName = sellerRequest.District,
                CountryCode = sellerRequest.CountryCode ?? "SA",
                BuildingNumber = "1", // Default value
                IdentityType = "CRN", // Default value
                IdentityNumber = sellerRequest.VatNumber, // Use VAT as identity
                AdditionalStreetAddress = string.Empty,
                PostalCode = "00000" // Default value
            };
        }
    }
}
