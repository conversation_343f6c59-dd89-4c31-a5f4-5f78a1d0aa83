﻿using Application.Contracts.IRepository;
using Domain.Entities;
using Microsoft.AspNetCore.Mvc;
using NuGet.Protocol;
using Persistence.Repositories;


namespace Presentation.Controllers
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    [ApiVersion("1.0")]
    public class SellerController : ControllerBase
    {
        private readonly ISupplierRepository _supplierRepository;
        public SellerController(ISupplierRepository supplierRepository)
        {
            _supplierRepository = supplierRepository;
        }
        // GET: api/<SellerController>
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                var Seller = await _supplierRepository.GetSupplierAsync();
                if (Seller==null)
                {
                    var response = new
                    {
                        StatusCode = 400,
                        Message = "There's no Seller added",
                    };

                    return NotFound(response.ToJson());
                }
                else
                {
                    return Ok(Seller);
                }

            }
            catch (Exception ex)
            {
                return BadRequest(ex.To<PERSON>son());
            }  
        }

        // POST api/<SellerController>
        [HttpPost]
        public async Task<IActionResult> Post([FromBody] Seller seller)
        {

            if (seller == null)
            {
                var response = new
                {
                    StatusCode = 422,
                    Message = "Seller received not in a valid format.",
                };
                return BadRequest(response);
            }
            else
            {
                try
                {
                    if (await _supplierRepository.IsSupplierFoundAsync())
                    {
                        var Conflictresponse = new
                        {
                            StatusCode = 409,
                            Message = "Seller already added so we can't add more than one seller",
                        };
                        return Conflict(Conflictresponse);
                    }
                    await _supplierRepository.AddSupplierAsync(seller);
                    var response = new
                    {
                        StatusCode = 200,
                        Message = "Seller Added successfully",
                    };
                    return Ok(response);

                }
                catch (Exception ex)
                {
                    return BadRequest(ex.ToJson());
                }
            }
        }

        // DELETE api/<SellerController>
        [HttpDelete]
        public async Task<IActionResult> Delete()
        {
            try
            {
                Seller seller = await _supplierRepository.GetSupplierAsync();
                if (seller != null)
                {
                    await _supplierRepository.DeleteSupplierAsync(seller);
                    var response = new
                    {
                        StatusCode = 200,
                        Message = "Seller Deleted Successfully",
                    };
                    return Ok(response);
                }
                else
                {
                    var response = new
                    {
                        StatusCode = 400,
                        Message = "There're no seller on DB",
                    };

                    return NotFound(response);
                }

            }
            catch (Exception ex)
            {

                return BadRequest(ex.ToJson());
            }
        }
    }
}
