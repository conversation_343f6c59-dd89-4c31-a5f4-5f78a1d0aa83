using Microsoft.Extensions.Options;

namespace ZatcaWebMvc.Services
{
    /// <summary>
    /// Configuration service for ZATCA API settings
    /// </summary>
    public class ApiConfigurationService
    {
        private readonly ZatcaApiOptions _options;
        private readonly ILogger<ApiConfigurationService> _logger;

        public ApiConfigurationService(IOptions<ZatcaApiOptions> options, ILogger<ApiConfigurationService> logger)
        {
            _options = options.Value;
            _logger = logger;
        }

        public string BaseUrl => _options.BaseUrl;
        public string? ApiKey => _options.ApiKey;
        public int TimeoutSeconds => _options.TimeoutSeconds;
        public int RetryAttempts => _options.RetryAttempts;
        public int RetryDelaySeconds => _options.RetryDelaySeconds;

        public bool IsConfigured => !string.IsNullOrEmpty(_options.BaseUrl);
        public bool HasApiKey => !string.IsNullOrEmpty(_options.ApiKey);

        public void ValidateConfiguration()
        {
            if (string.IsNullOrEmpty(_options.BaseUrl))
            {
                throw new InvalidOperationException("ZATCA API BaseUrl is not configured");
            }

            if (!Uri.TryCreate(_options.BaseUrl, UriKind.Absolute, out _))
            {
                throw new InvalidOperationException("ZATCA API BaseUrl is not a valid URL");
            }

            if (_options.TimeoutSeconds <= 0)
            {
                throw new InvalidOperationException("ZATCA API TimeoutSeconds must be greater than 0");
            }

            _logger.LogInformation("ZATCA API configuration validated successfully");
        }
    }

    /// <summary>
    /// Configuration options for ZATCA API
    /// </summary>
    public class ZatcaApiOptions
    {
        public const string SectionName = "ZatcaApi";

        public string BaseUrl { get; set; } = "http://localhost:5147";
        public string? ApiKey { get; set; }
        public int TimeoutSeconds { get; set; } = 30;
        public int RetryAttempts { get; set; } = 3;
        public int RetryDelaySeconds { get; set; } = 2;
    }
}
