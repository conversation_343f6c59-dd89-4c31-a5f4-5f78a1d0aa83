using Microsoft.AspNetCore.Mvc;
using ZatcaWebMvc.Models;
using ZatcaWebMvc.Services;

namespace ZatcaWebMvc.Controllers
{
    /// <summary>
    /// Controller for managing invoices
    /// </summary>
    public class InvoiceController : Controller
    {
        private readonly IZatcaApiService _zatcaApiService;
        private readonly ILogger<InvoiceController> _logger;

        public InvoiceController(IZatcaApiService zatcaApiService, ILogger<InvoiceController> logger)
        {
            _zatcaApiService = zatcaApiService;
            _logger = logger;
        }

        /// <summary>
        /// Display list of all invoices
        /// </summary>
        public async Task<IActionResult> Index()
        {
            try
            {
                var response = await _zatcaApiService.GetAllInvoicesAsync();
                
                if (response.Success && response.Data != null)
                {
                    return View(response.Data);
                }
                
                ViewBag.ErrorMessage = response.Message;
                return View(new List<InvoiceSubmissionResponse>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading invoices");
                ViewBag.ErrorMessage = "An error occurred while loading invoices.";
                return View(new List<InvoiceSubmissionResponse>());
            }
        }

        /// <summary>
        /// Display invoice details
        /// </summary>
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var response = await _zatcaApiService.GetInvoiceAsync(id);
                
                if (response.Success && response.Data != null)
                {
                    return View(response.Data);
                }
                
                ViewBag.ErrorMessage = response.Message;
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading invoice {InvoiceId}", id);
                ViewBag.ErrorMessage = "An error occurred while loading the invoice.";
                return NotFound();
            }
        }

        /// <summary>
        /// Display create invoice form
        /// </summary>
        public IActionResult Create()
        {
            var model = new InvoiceCreateRequest
            {
                InvoiceDate = DateTime.Now
            };
            return View(model);
        }

        /// <summary>
        /// Handle invoice creation
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(InvoiceCreateRequest model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var response = await _zatcaApiService.CreateInvoiceAsync(model);
                
                if (response.Success)
                {
                    TempData["SuccessMessage"] = "Invoice created successfully!";
                    return RedirectToAction(nameof(Index));
                }
                
                // Add API errors to ModelState
                foreach (var error in response.Errors)
                {
                    ModelState.AddModelError("", error);
                }
                
                ViewBag.ErrorMessage = response.Message;
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating invoice");
                ModelState.AddModelError("", "An error occurred while creating the invoice.");
                return View(model);
            }
        }
    }
}
