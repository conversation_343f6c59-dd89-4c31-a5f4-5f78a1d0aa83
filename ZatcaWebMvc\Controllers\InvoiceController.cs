using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ZatcaWebMvc.Models;
using ZatcaWebMvc.Services;

namespace ZatcaWebMvc.Controllers
{
    /// <summary>
    /// Controller for managing invoices with enhanced functionality
    /// </summary>
    [Authorize]
    public class InvoiceController : Controller
    {
        private readonly IZatcaApiService _zatcaApiService;
        private readonly ILogger<InvoiceController> _logger;

        public InvoiceController(IZatcaApiService zatcaApiService, ILogger<InvoiceController> logger)
        {
            _zatcaApiService = zatcaApiService;
            _logger = logger;
        }

        /// <summary>
        /// Display list of invoices with enhanced pagination and filtering
        /// </summary>
        public async Task<IActionResult> Index(int page = 1, int pageSize = 10, string searchTerm = "", string status = "")
        {
            try
            {
                var response = await _zatcaApiService.GetInvoicesAsync(page, pageSize);

                if (response.Success && response.Data != null)
                {
                    var invoices = response.Data;

                    // Apply client-side filtering if needed
                    if (!string.IsNullOrEmpty(searchTerm))
                    {
                        invoices = invoices.Where(i =>
                            i.InvoiceNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                            i.CustomerName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                        ).ToList();
                    }

                    if (!string.IsNullOrEmpty(status))
                    {
                        invoices = invoices.Where(i => i.Status.Equals(status, StringComparison.OrdinalIgnoreCase)).ToList();
                    }

                    ViewBag.CurrentPage = page;
                    ViewBag.PageSize = pageSize;
                    ViewBag.SearchTerm = searchTerm;
                    ViewBag.Status = status;
                    ViewBag.TotalCount = invoices.Count;

                    return View(invoices);
                }

                ViewBag.ErrorMessage = response.Message;
                return View(new List<InvoiceSubmissionResponse>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading invoices");
                ViewBag.ErrorMessage = "An error occurred while loading invoices.";
                return View(new List<InvoiceSubmissionResponse>());
            }
        }

        /// <summary>
        /// Display invoice details with enhanced information
        /// </summary>
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var response = await _zatcaApiService.GetInvoiceAsync(id);

                if (response.Success && response.Data != null)
                {
                    ViewBag.CanSubmit = !response.Data.ReportedToZatca;
                    return View(response.Data);
                }

                ViewBag.ErrorMessage = response.Message;
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading invoice {InvoiceId}", id);
                ViewBag.ErrorMessage = "An error occurred while loading the invoice.";
                return NotFound();
            }
        }

        /// <summary>
        /// Display create invoice form with line items support
        /// </summary>
        public IActionResult Create()
        {
            var model = new InvoiceCreateRequest
            {
                InvoiceDate = DateTime.Now
            };

            // Initialize with one empty line item
            ViewBag.LineItems = new List<InvoiceLineItem>
            {
                new InvoiceLineItem()
            };

            return View(model);
        }

        /// <summary>
        /// Handle invoice creation
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(InvoiceCreateRequest model)
        {
            if (!ModelState.IsValid)
            {
                ViewBag.LineItems = new List<InvoiceLineItem> { new InvoiceLineItem() };
                return View(model);
            }

            try
            {
                var response = await _zatcaApiService.CreateInvoiceAsync(model);

                if (response.Success && response.Data != null)
                {
                    TempData["SuccessMessage"] = "Invoice created successfully.";
                    return RedirectToAction(nameof(Details), new { id = response.Data.Id });
                }

                ModelState.AddModelError("", response.Message);
                ViewBag.LineItems = new List<InvoiceLineItem> { new InvoiceLineItem() };
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating invoice");
                ModelState.AddModelError("", "An error occurred while creating the invoice.");
                ViewBag.LineItems = new List<InvoiceLineItem> { new InvoiceLineItem() };
                return View(model);
            }
        }

        /// <summary>
        /// Submit invoice to ZATCA with enhanced feedback
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Submit(int id)
        {
            try
            {
                var response = await _zatcaApiService.SubmitInvoiceAsync(id);

                if (response.Success)
                {
                    TempData["SuccessMessage"] = "Invoice submitted to ZATCA successfully.";
                }
                else
                {
                    TempData["ErrorMessage"] = $"Failed to submit invoice to ZATCA: {response.Message}";
                }

                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting invoice {InvoiceId} to ZATCA", id);
                TempData["ErrorMessage"] = "An error occurred while submitting the invoice to ZATCA.";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        /// <summary>
        /// Get invoice status for AJAX calls
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetStatus(int id)
        {
            try
            {
                var response = await _zatcaApiService.GetInvoiceAsync(id);

                if (response.Success && response.Data != null)
                {
                    return Json(new
                    {
                        success = true,
                        status = response.Data.Status,
                        reportingStatus = response.Data.ReportingStatus,
                        clearanceStatus = response.Data.ClearanceStatus,
                        reportedToZatca = response.Data.ReportedToZatca
                    });
                }

                return Json(new { success = false, message = response.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice status for {InvoiceId}", id);
                return Json(new { success = false, message = "An error occurred while getting invoice status." });
            }
        }
    }
}
