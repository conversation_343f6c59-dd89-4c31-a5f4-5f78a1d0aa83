using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using ZatcaWebMvc.Models;
using ZatcaWebMvc.Services;

namespace ZatcaWebMvc.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly IZatcaApiService _zatcaApiService;

    public HomeController(ILogger<HomeController> logger, IZatcaApiService zatcaApiService)
    {
        _logger = logger;
        _zatcaApiService = zatcaApiService;
    }

    public async Task<IActionResult> Index()
    {
        var dashboardModel = new DashboardViewModel();

        try
        {
            // Check API health status
            dashboardModel.ApiHealthy = await _zatcaApiService.IsApiHealthyAsync();

            if (dashboardModel.ApiHealthy)
            {
                // Get dashboard statistics
                var invoicesResponse = await _zatcaApiService.GetAllInvoicesAsync();
                if (invoicesResponse.Success && invoicesResponse.Data != null)
                {
                    var invoices = invoicesResponse.Data;
                    dashboardModel.TotalInvoices = invoices.Count;
                    dashboardModel.PendingInvoices = invoices.Count(i => i.Status == "Pending");
                    dashboardModel.SubmittedInvoices = invoices.Count(i => i.Status == "Submitted");
                    dashboardModel.TotalRevenue = invoices.Where(i => i.Status == "Submitted").Sum(i => i.TotalAmount);
                    dashboardModel.RecentInvoices = invoices.OrderByDescending(i => i.IssueDate).Take(5).ToList();
                }

                var certificatesResponse = await _zatcaApiService.GetAllCertificatesAsync();
                if (certificatesResponse.Success && certificatesResponse.Data != null)
                {
                    dashboardModel.TotalCertificates = certificatesResponse.Data.Count;
                }

                var sellersResponse = await _zatcaApiService.GetAllSellersAsync();
                if (sellersResponse.Success && sellersResponse.Data != null)
                {
                    dashboardModel.TotalSellers = sellersResponse.Data.Count;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard data");
            dashboardModel.ApiHealthy = false;
        }

        return View(dashboardModel);
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
