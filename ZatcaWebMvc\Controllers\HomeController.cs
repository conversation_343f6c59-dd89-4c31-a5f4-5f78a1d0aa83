using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using ZatcaWebMvc.Models;
using ZatcaWebMvc.Services;

namespace ZatcaWebMvc.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly IZatcaApiService _zatcaApiService;

    public HomeController(ILogger<HomeController> logger, IZatcaApiService zatcaApiService)
    {
        _logger = logger;
        _zatcaApiService = zatcaApiService;
    }

    public async Task<IActionResult> Index()
    {
        // Check API health status
        ViewBag.ApiHealthy = await _zatcaApiService.IsApiHealthyAsync();
        return View();
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
