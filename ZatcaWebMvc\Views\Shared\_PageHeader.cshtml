@{
    var title = ViewData["Title"]?.ToString() ?? "Page Title";
    var subtitle = ViewData["Subtitle"]?.ToString();
    var icon = ViewData["Icon"]?.ToString();
    var actions = ViewData["Actions"]?.ToString();
    var breadcrumbs = ViewData["Breadcrumbs"] as List<(string Text, string? Url, bool IsActive)>;
    var showBreadcrumbs = ViewData["ShowBreadcrumbs"]?.ToString() != "false" && breadcrumbs?.Any() == true;
    var headerClass = ViewData["HeaderClass"]?.ToString() ?? "bg-white";
    var borderBottom = ViewData["BorderBottom"]?.ToString() != "false";
}

<div class="page-header @headerClass @(borderBottom ? "border-bottom" : "") py-4">
    <div class="container-fluid">
        @if (showBreadcrumbs)
        {
            <div class="row mb-3">
                <div class="col-12">
                    @await Html.PartialAsync("_Breadcrumb", breadcrumbs)
                </div>
            </div>
        }
        
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    @if (!string.IsNullOrEmpty(icon))
                    {
                        <div class="page-icon me-3">
                            <i class="@icon text-primary fs-2"></i>
                        </div>
                    }
                    <div>
                        <h1 class="page-title mb-1">@title</h1>
                        @if (!string.IsNullOrEmpty(subtitle))
                        {
                            <p class="page-subtitle text-muted mb-0">@subtitle</p>
                        }
                    </div>
                </div>
            </div>
            
            @if (!string.IsNullOrEmpty(actions))
            {
                <div class="col-md-4">
                    <div class="page-actions text-md-end">
                        @Html.Raw(actions)
                    </div>
                </div>
            }
        </div>
    </div>
</div>


