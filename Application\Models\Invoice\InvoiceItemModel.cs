﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Models.Invoice
{
    public class InvoiceItemModel
    {
        public InvoiceItemModel()
        {
            //  Id = Guid.NewGuid().ToString();
        }
        /// <summary>
        /// Item Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// Item Description or Name
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// Quantity of the item in the invoice Details
        /// </summary>
        public string Qty { get; set; }
        /// <summary>
        /// Item Total price After applying discount and VAT
        /// </summary>
        public string Price { get; set; }//gross price
        /// <summary>
        /// Item unit price 
        /// </summary>
        public string NetValue { get; set; }
        /// <summary>
        /// Total Discount applied to the row 
        /// </summary>
        public string TotalDiscount { get; set; }
        /// <summary>
        /// Tax as a vlue applied to row 
        /// </summary>
        public string VAT { get; set; }
        /// <summary>
        /// The percentage of the Tax applied to the item row
        /// </summary>
        public string VATPercentage { get; set; }
        /// <summary>
        /// Tax have more than one category
        /// S - Standard Rated Supplies (VAT Category )
        /// Z - Zero Rated Supplies
        /// E - Exempt Supplies
        /// O - Outside Scope of VAT
        /// N - Not Subject to VAT
        /// </summary>
        public string TaxCategory { get; set; } = "S";
        //public int Index { get; set; }
        //public string ProductName { get; set; }
        //public double Quantity { get; set; }
        //public double NetPrice { get; set; }
        //public double LineDiscount { get; set; }
        //public double PriceDiscount { get; set; }
        /// <summary>
        /// The line VAT amount (KSA-11) must be Invoice line net amount (BT-131) x(Line VAT rate (BT152)/100)
        /// </summary>
        //public double TaxAmount { get; set; }
        //public double TotalWithTax { get; set; }

        /// <summary>
        /// The invoice line net amount without VAT, and inclusive of line level allowance.
        /// Item line net amount (BT-131) = ((Item net price (BT-146) ÷ Item price base quantity(BT-149)) 
        ///     × (Invoiced Quantity (BT-129)) − Invoice line allowance amount(BT-136)
        /// </summary>
        //public double TotalWithoutTax { get; set; }

        //public double GrossPrice
        //{
        //    get
        //    {
        //        return NetValue + TotalDiscount;
        //    }
        //}


        //public double Tax { get; set; }
        //public string TaxCategory { get; set; } = "S";
        //public string TaxCategoryReasonCode { set; get; }
        //public string TaxCategoryReason { set; get; }
    }
}
